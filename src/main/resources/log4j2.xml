<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
	<Appenders>
		<Console name="STDOUT" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</Console>
		<File name="test1" fileName="logs/TestCase1.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test2" fileName="logs/TestCase2.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test3" fileName="logs/TestCase3.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test4" fileName="logs/TestCase4.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test5" fileName="logs/TestCase5.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test6" fileName="logs/TestCase6.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test7" fileName="logs/TestCase7.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test8" fileName="logs/TestCase8.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test9" fileName="logs/TestCase9.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test10" fileName="logs/TestCase10.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test11" fileName="logs/TestCase11.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test12" fileName="logs/TestCase12.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test13" fileName="logs/TestCase13.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test14" fileName="logs/TestCase14.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test15" fileName="logs/TestCase15.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
		<File name="test16" fileName="logs/TestCase16.log" >
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</File>
	</Appenders>
	<Loggers>
		<logger name="com.sungoin.cti.client" level="DEBUG" additivity="false">
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase1" level="DEBUG" additivity="false">
			<appender-ref ref="test1" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase2" level="DEBUG" additivity="false">
			<appender-ref ref="test2" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase3" level="DEBUG" additivity="false">
			<appender-ref ref="test3" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase4" level="DEBUG" additivity="false">
			<appender-ref ref="test4"/>
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase5" level="DEBUG" additivity="false">
			<appender-ref ref="test5" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase6" level="DEBUG" additivity="false">
			<appender-ref ref="test6" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase7" level="DEBUG" additivity="false">
			<appender-ref ref="test7" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase8" level="DEBUG" additivity="false">
			<appender-ref ref="test8" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase9" level="DEBUG" additivity="false">
			<appender-ref ref="test9" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase10" level="DEBUG" additivity="false">
			<appender-ref ref="test10" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase11" level="DEBUG" additivity="false">
			<appender-ref ref="test11" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase12" level="DEBUG" additivity="false">
			<appender-ref ref="test12" />
			<appender-ref ref="STDOUT" />
		</logger>
        <logger name="com.sungoin.cti.client.demo.TestCase13" level="DEBUG" additivity="false">
			<appender-ref ref="test13" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase14" level="DEBUG" additivity="false">
			<appender-ref ref="test14" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase15" level="DEBUG" additivity="false">
			<appender-ref ref="test15" />
			<appender-ref ref="STDOUT" />
		</logger>
		<logger name="com.sungoin.cti.client.demo.TestCase16" level="DEBUG" additivity="false">
			<appender-ref ref="test16" />
			<appender-ref ref="STDOUT" />
		</logger>
		<Root level="WARN">
			<AppenderRef ref="STDOUT"/>
		</Root>
	</Loggers>
</Configuration>