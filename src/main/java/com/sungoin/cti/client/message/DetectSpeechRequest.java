/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.client.message;

/**
 *
 * <AUTHOR>
 */
public class DetectSpeechRequest extends BaseRequest {
    private final String file;
    private final String timeout;

    public DetectSpeechRequest(String deviceID, int lsh, String threadName, String file, long timeout) {
        super(deviceID, MessageType.DETECTSPEECH, lsh, threadName);
        this.file = file;
        this.timeout = String.valueOf(timeout);
    }

    public String getFile() {
        return file;
    }

    public String getTimeout() {
        return timeout;
    }
    
}
