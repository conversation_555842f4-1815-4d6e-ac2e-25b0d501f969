/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.Configuration;
import com.sungoin.cti.client.Dispatcher;
import com.sungoin.cti.client.util.WaitCondition;
import com.sungoin.cti.client.util.WaitUtil;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-13 MDT 线程
 */
public class MessageDispatcher extends Thread implements Dispatcher {

	private final static Logger log = LoggerFactory.getLogger(MessageDispatcher.class);
	private final static MessageDispatcher instance = new MessageDispatcher();
	private final BlockingQueue<BaseResponse> queue = new ArrayBlockingQueue<BaseResponse>(Configuration.getMessageQueueSize());

	public static MessageDispatcher getInstance() {
		return instance;
	}

	private MessageDispatcher() {
		super("CTI Message Dispathcer Thread");
	}

	@Override
	public void run() {
		log.info("MDT Thread started...");
		while (CallManager.getInstance().isRunning()) {
			doDispatch();
		}
	}

	private void doDispatch() {
		try {
			BaseResponse response = queue.take();
			String threadName = response.getThreadName();
			int lsh = response.getLsh();
			if (response.getType().equals(MessageType.MAKECALL)) {
				lsh = 0;
			}
			if (WaitUtil.isWaiting(threadName, WaitCondition.MESSAGE, response.getType(), lsh)) {
				WaitUtil.notifyWaiter(threadName, WaitCondition.MESSAGE, response.getType(), lsh, response);
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
	}

	@Override
	public void dispatch(BaseResponse response) {
		boolean notFull = queue.offer(response);
		if(!notFull) {
			log.warn("消息队列满！当前队列数：{},请调大队列数量！",Configuration.getMessageQueueSize());
		}
	}
}
