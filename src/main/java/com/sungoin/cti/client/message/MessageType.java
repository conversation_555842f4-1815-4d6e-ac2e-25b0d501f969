/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class MessageType {

	public static final String ALERT = "ALERT";
    public static final String MAKECALL = "MAKECALL";
    public static final String PLAY = "PLAY";
    public static final String CONNECT = "CONNECT";
    public static final String DISCONNECT = "DISCONNECT";
    public static final String STOPPLAY = "STOPPLAY";
    public static final String STOPRECORD = "STOPRECORD";
    public static final String ANSWER = "ANSWER";
    public static final String RECORD = "RECORD";
    public static final String ONHOOK = "ONHOOK";
    public static final String RECEIVEDTMF = "RECEIVEDTMF";
    public static final String STOPRECEIVEDTMF = "STOPRECEIVEDTMF";
    public static final String PLAYDTMF = "PLAYDTMF";
	public static final String PLAYNUMBER = "PLAYNUMBER";
    public static final String CREATECONF = "CREATECONF";
    public static final String JOINCONF = "JOINCONF";
	public static final String LEAVECONF = "LEAVECONF";
    
    public static final String BROADCAST = "BROADCAST";
    public static final String DETECTSPEECH = "DETECTSPEECH";
    public static final String STOPDETECTSPEECH = "STOPDETECTSPEECH";

    //调用CTI Server上其他功能的请求统一用此类型
    public static final String FUNCTION = "FUNCTION";
}
