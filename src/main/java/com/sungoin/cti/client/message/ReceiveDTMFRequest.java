/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

/**
 *
 * <AUTHOR>
 */
public class ReceiveDTMFRequest extends BaseRequest {

    private int maxdigits;
    private String terminatedchar;

    public int getMaxdigits() {
        return maxdigits;
    }

    public void setMaxdigits(int maxdigits) {
        this.maxdigits = maxdigits;
    }

    public String getTerminatedchar() {
        return terminatedchar;
    }

    public void setTerminatedchar(String terminatedchar) {
        this.terminatedchar = terminatedchar;
    }

	public ReceiveDTMFRequest(String deviceID, int lsh, int maxdigits, String terminatedchar, String threadName) {
		super(deviceID, MessageType.RECEIVEDTMF, lsh, threadName);
		this.maxdigits = maxdigits;
		this.terminatedchar = terminatedchar;
	}

}
