/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

import com.sungoin.cti.client.api.CONF_MODE;

/**
 *
 * <AUTHOR>
 */
public class ConferenceRequest extends BaseRequest{
    private final  String mode;
    private String confID;

    public ConferenceRequest(String deviceID,  int lsh, CONF_MODE mode, String threadName) {
        super(deviceID, MessageType.CREATECONF, lsh, threadName);
        this.mode = mode.toString();
    }

    public String getConfID() {
        return confID;
    }

    public void setConfID(String confID) {
        this.confID = confID;
    }

    public String getMode() {
        return mode;
    }
    
}
