/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

import com.sungoin.cti.client.event.EventType;

/**
 *
 * <AUTHOR>
 */
public class RecordRequest extends BaseRequest {

    private String filePath;
    private int length;
    private boolean isAppend;

    public boolean isIsAppend() {
        return isAppend;
    }

    public void setIsAppend(boolean isAppend) {
        this.isAppend = isAppend;
    }
    
    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

	public RecordRequest(String deviceID, int lsh, String filePath, int length, boolean isAppend, String threadName) {
		super(deviceID, MessageType.RECORD, lsh, threadName);
		this.filePath = filePath;
		this.length = length;
		this.isAppend = isAppend;
	}

}
