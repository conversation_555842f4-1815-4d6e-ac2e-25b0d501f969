/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class ConnectRequest extends BaseRequest {

    private String calleeDeviceID;
    private int calleeLsh;
    private boolean sameDsp;

    public int getCalleeLsh() {
        return calleeLsh;
    }

    public void setCalleeLsh(int calleeLsh) {
        this.calleeLsh = calleeLsh;
    }
    

	public ConnectRequest(int calleeLsh,String deviceID, int lsh, String calleeDeviceID, String threadName) {
		super(deviceID, MessageType.CONNECT, lsh, threadName);
		this.calleeDeviceID = calleeDeviceID;
        this.calleeLsh = calleeLsh;
        this.sameDsp = true;
	}
    
    public ConnectRequest(int calleeLsh,String deviceID, int lsh, String calleeDeviceID, String threadName, boolean sameDsp) {
		super(deviceID, MessageType.CONNECT, lsh, threadName);
		this.calleeDeviceID = calleeDeviceID;
        this.calleeLsh = calleeLsh;
        this.sameDsp = sameDsp;
	}
    

    public String getCalleeDeviceID() {
        return calleeDeviceID;
    }

    public void setCalleeDeviceID(String calleeDeviceID) {
        this.calleeDeviceID = calleeDeviceID;
    }

    public boolean isSameDsp() {
        return sameDsp;
    }

    public void setSameDsp(boolean sameDsp) {
        this.sameDsp = sameDsp;
    }
}
