/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

import com.sungoin.cti.client.socket.MinaClient;
import com.sungoin.cti.client.util.JsonHelper;
import com.sungoin.cti.client.util.WaitCondition;
import com.sungoin.cti.client.util.WaitUtil;

/**
 *
 * <AUTHOR>
 */
public class BaseRequest {

	protected String threadName;
	protected int lsh = 0;
	protected String deviceID;
	protected String operateType;

	public BaseRequest(String deviceID, String operateType, int lsh) {
		this(deviceID, operateType, lsh, Thread.currentThread().getName());
	}

	public BaseRequest(String deviceID, String operateType, int lsh, String threadName) {
		this.deviceID = deviceID;
		this.operateType = operateType;
		this.lsh = lsh;
		this.threadName = threadName;
	}

	public BaseRequest() {

	}

	public String getThreadName() {
		return threadName;
	}

	public void setThreadName(String threadName) {
		this.threadName = threadName;
	}

	/**
	 * 向服务端发送消息，不等待服务端返回结果
	 */
	public void send() {
		String message = JsonHelper.Object2Json(this);
		MinaClient.getInstance().sendMessage(message);
	}

	/**
	 * 向服务端发送消息，等待服务端返回结果
	 *
	 * @param timeoutMillis
	 * @return
	 */
	public BaseResponse submit(long timeoutMillis) {
		try {
			String message = JsonHelper.Object2Json(this);
			MinaClient.getInstance().sendMessageAsync(message);
			return WaitUtil.await(this.getThreadName(), WaitCondition.MESSAGE, this.operateType, this.lsh, timeoutMillis);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return null;
	}

	public int getLsh() {
		return lsh;
	}

	public void setLsh(int lsh) {
		this.lsh = lsh;
	}

	public String getDeviceID() {
		return deviceID;
	}

	public void setDeviceID(String deviceID) {
		this.deviceID = deviceID;
	}

	public String getOperateType() {
		return operateType;
	}

	public void setOperateType(String operateType) {
		this.operateType = operateType;
	}
}
