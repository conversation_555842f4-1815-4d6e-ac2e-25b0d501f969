/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class PlayRequest extends BaseRequest {

    private final String filePath;
    private final String loop;
    private final String isQueue;
    private final int maxSecond;

    public int getMaxSecond() {
        return maxSecond;
    }
    

    public PlayRequest(String deviceID, int lsh, String filePath, String loop, String isQueue,int maxSecond, String threadName) {
        super(deviceID, MessageType.PLAY, lsh, threadName);
        this.filePath = filePath;
        this.loop = loop;
        this.isQueue = isQueue;
        this.maxSecond =maxSecond;
    }

    public String getFilePath() {
        return filePath;
    }

    public String getLoop() {
        return loop;
    }

    public String getIsQueue() {
        return isQueue;
    }

}
