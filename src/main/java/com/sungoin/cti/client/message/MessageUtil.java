/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

import java.util.Map;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class MessageUtil {

	public static final BaseResponse parseResponse(Map dataMap) {
		String messageType = (String) dataMap.get("messageType");
		BaseResponse rep = null;
		int lsh = Integer.parseInt(dataMap.get("lsh").toString());
		int eventState = Integer.parseInt(dataMap.get("eventState").toString());
		String threadName = (String) dataMap.get("threadName");
		String deviceID = (String) dataMap.get("deviceID");
        int errorCode = 0;
        if(dataMap.containsKey("errorCode")) {
            errorCode = Integer.parseInt(dataMap.get("errorCode").toString());
        }
		if (!messageType.equals(MessageType.FUNCTION)) {
            //如果是加入会议或创建会议
            if(messageType.equals(MessageType.CREATECONF) || messageType.equals(MessageType.JOINCONF) ){
                String conID = (String) dataMap.get("conID");
                rep = new ConferenceResponse(conID, lsh, eventState, threadName, deviceID, messageType, errorCode);
            }else {
            	//返回通用response
			  rep = new BaseResponse(lsh, eventState, threadName, deviceID, messageType, errorCode);
            }
		
		}
		return rep;
	}
}
