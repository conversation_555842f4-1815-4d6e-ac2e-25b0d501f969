/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.message;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class BaseResponse {

	protected String threadName;
	protected int lsh = 0;
	protected String deviceID;
	protected int eventState;
	protected String type;
    protected int errorCode;

	public int getLsh() {
		return lsh;
	}

	public BaseResponse() {

	}
    
    public BaseResponse(int lsh, int eventState, String threadName, String deviceID, String type, int errorCode) {
		this.lsh = lsh;
		this.eventState = eventState;
		this.deviceID = deviceID;
		this.threadName = threadName;
		this.type = type;
        this.errorCode = errorCode;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public void setLsh(int lsh) {
		this.lsh = lsh;
	}

	public String getDeviceID() {
		return deviceID;
	}

	public void setDeviceID(String deviceID) {
		this.deviceID = deviceID;
	}

	public int getEventState() {
		return eventState;
	}

	public void setEventState(int eventState) {
		this.eventState = eventState;
	}

	public String getThreadName() {
		return threadName;
	}

	public void setThreadName(String threadName) {
		this.threadName = threadName;
	}

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

	@Override
	public String toString() {
		return "BaseResponse{" + "lsh=" + lsh + ", eventState=" + eventState + ", deviceID=" + deviceID + ", type=" + type + '}';
	}
}
