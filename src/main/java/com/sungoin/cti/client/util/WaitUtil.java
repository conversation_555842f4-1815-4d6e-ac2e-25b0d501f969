/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.util;

import com.sungoin.cti.client.message.BaseResponse;
import com.sungoin.cti.client.exception.AlreadyWaitingException;
import com.sungoin.cti.client.exception.TimeoutException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class WaitUtil {

	static final Map<String, WaitObject> waitMap = new ConcurrentHashMap<String, WaitObject>();

	public static BaseResponse await(String threadName, WaitCondition condition, String type, int lsh, long timeMillis) throws AlreadyWaitingException,
		TimeoutException {
		String key = threadName + condition + type + lsh;
		if (waitMap.containsKey(key)) {
			throw new AlreadyWaitingException("already waiting same condition...");
		}
		WaitObject wo = new WaitObject();
		waitMap.put(key, wo);
		synchronized (wo.getSync()) {
			while (!wo.isAwoken()) {
				try {
					wo.getSync().wait(timeMillis);
				} catch (Exception ex) {
					//interrupted
				}
				waitMap.remove(key);
				if (!wo.isAwoken()) {
					throw new TimeoutException("time out :" + timeMillis);
				}
			}
			return wo.getResult();
		}
	}

	public static boolean isWaiting(String threadName, WaitCondition condition, String type, int lsh) {
		return waitMap.containsKey(threadName + condition + type + lsh);
	}

	public static void notifyWaiter(String threadName, WaitCondition condition, String type, int lsh, BaseResponse result) {
		if (result == null) {
			throw new NullPointerException("notify result can not be null!");
		}
		WaitObject wo = waitMap.get(threadName + condition + type + lsh);
		wo.notifyWaiter(result);
	}

	static class WaitObject {

		private final Object sync = new Object();
		private BaseResponse result;
		private boolean awoken;

		public Object getSync() {
			return sync;
		}

		public BaseResponse getResult() {
			return result;
		}

		public void setResult(BaseResponse result) {
			this.result = result;
		}

		public boolean isAwoken() {
			return awoken;
		}

		public void setAwoken(boolean awoken) {
			this.awoken = awoken;
		}

		public void notifyWaiter(BaseResponse result) {
			synchronized (sync) {
				this.result = result;
				this.awoken = true;
				sync.notifyAll();
			}
		}
	}
}
