/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CONF_MODE;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.Conference;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import java.util.logging.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 1.呼入，输出主叫号码 2.系统摘记，打印摘记事件 3.呼叫坐席A，建立会话 4.异步录音 5.通话转接 6.通话保持 7.重新建立会话
 * 8.添加来电通话参数 9.结束异步录音 10.异步录音 11.结束异步录音 12.对来电挂机
 *
 * <AUTHOR> 2015-5-14
 */
public class TestCase13 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase13.class);
	private final Call call;

	public TestCase13(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：主叫 is {}, 被叫 is {}", call.getCaller(), call.getCallee());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			call.attachTeleThread(this);
			call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\jsonconf.wav", false, false, true, 0, 0);
			log.debug("对呼入用户同步收码...");
			String str = call.receiveDTMFSync(1, "*", 20);
			log.debug("收码结果为 {}", str);
			call.stopPlay();
			if ("1".equals(str)) {
				call.createConf(CONF_MODE.ADD);
			} else if ("2".equals(str)) {
				call.createConf(CONF_MODE.LISTEN);
			} else if ("3".equals(str)) {
				call.createConf(CONF_MODE.SPEAKONLY);
			}

			log.debug("异步收码...");
			call.receiveDTMFAsync();

			log.debug("主线程结束。。。。");
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			String eventType = event.getType();
			if (eventType.equals(EventType.JOINCONF)) {
				log.debug(event.getCall().getCallee() + "加入会议。。。");

			} else if (eventType.equals(EventType.LEAVECONF)) {
				log.debug(event.getCall().getCallee() + "离开会议。。。");
			} else if (eventType.equals(EventType.CLEARCONF)) {
				log.debug("会议结束。。。");
			}
		}

	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			String eventType = event.getType();
			if (eventType.equals(EventType.JOINCONF)) {
				log.debug(event.getCall().getCallee() + "加入会议。。。");
			} else if (eventType.equals(EventType.LEAVECONF)) {
				log.debug(event.getCall().getCallee() + "离开会议。。。");
			} else if (eventType.equals(EventType.CLEARCONF)) {
				log.debug("会议结束。。。");
			} else if (eventType.equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				log.debug(" receive dtmf is {} !", ev.getDtmf());
				Call call = ev.getCall();
				String key = "dtmf" + call.getCaller();
				if ("*".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					log.debug("收码结果为 {}", str);
					call.setParam(key, null);
					Conference conf = (Conference)call.getParam("conf");
					if ("1".equals(str)) {
						call.joinConf(conf, CONF_MODE.ADD);
					} else if ("2".equals(str)) {
						call.joinConf(conf, CONF_MODE.LISTEN);
					} else if ("3".equals(str)) {
						call.joinConf(conf, CONF_MODE.SPEAKONLY);
					}
				} else if ("#".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					log.debug("收码结果为 {}", str);
					call.setParam(key, null);
					if (str.length() == 11) {
						log.debug("呼叫号码:{}", str);
						Call call2 = CallManager.getInstance().makeCall("31233490", str, false, 30);
						if (call2 != null) {
							call2.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\jsonconf.wav", false, false, true, 0, 0);
							log.debug("异步收码...");
							call2.receiveDTMFAsync();
							call2.setParam("conf", call.getCurrentConf());
						}
					}
				} else {
					log.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(), call.getParam(key));
					String dtmf = call.getParam(key) != null ? call.getParam(key) + ev.getDtmf() : ev.getDtmf();
					call.setParam(key, dtmf);
				}
			}
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase13(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
			log.debug("挂机取得参数：姓名={}", call.getParam("姓名"));
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}
}
