/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TestCase8 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase8.class);
	private final Call call;

	public TestCase8(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			((MyThreadListener) this.getListener()).setMainThread(this);
			call.setParam("threadName", this.getName());
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：caller is {}, callee is {}, state={}", call.getCaller(), call.getCallee(), call.getState());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			log.debug("主叫号码为：{}", call.getCaller());
//			Call call2 = CallManager.getInstance().makeCall("31233490", "13167062230", false, 30);
//			if (call2 != null) {
//				log.debug("坐席接通...");
//				call.connectCall(call2);
//			} else {
				log.debug("坐席未接进入留言,放留言音...");
				call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\2012012108595.wav", false, false, true, 0, 27);
				log.debug("开始录音...");
				call.record("C:\\record\\1.wav", -1, false);
				log.debug("对呼入用户异步收码...");
				this.await(500);
				call.receiveDTMFAsync();
//			}

		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			log.debug("主线程结束。。。。");
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		public TestCase8 mainThread;

		public void setMainThread(TestCase8 mainThread) {
			this.mainThread = mainThread;
		}

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...eventType={},callState={}", event.getType(), event.getCall().getState());
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			if (event.getType().equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				log.debug(" receive dtmf is {} !", ev.getDtmf());
				Call call = ev.getCall();
				String key = "dtmf" + call.getCaller();
				if ("#".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					log.debug("收码结果为 {}", str);
					if ("01".equals(str)) {
						log.debug("留言结束...");
						call.stopRecord();
						log.debug("播放留言。。。");
						call.play("C:\\record\\1.wav", false, false, true, 0, 0);
					}
					call.setParam(key,"");
				} else {
					log.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(), call.getParam(key));
					String dtmf = call.getParam(key) != null ? call.getParam(key) + ev.getDtmf() : ev.getDtmf();
					call.setParam(key, dtmf);
				}
			}
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase8(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
		}

	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}

}
