/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TestCase9 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase9.class);
	private final Call call;

	public TestCase9(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			((MyThreadListener) this.getListener()).setMainThread(this);
			call.setParam("threadName", this.getName());
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：caller is {}, callee is {}, state={}", call.getCaller(), call.getCallee(), call.getState());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\dudu.wav", false, false, true, 0, 0);
			String str = call.receiveDTMFSync(1, "#", 30);
			log.debug("str is {}...", str);
			if ("1".equals(str)) {
				log.debug("进入留言,放留言音...");
				call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\backgroud.wav", false, false, true, 0, 0);
				log.debug("开始录音...");
				call.record("C:\\record\\1.wav", -1, false);
				log.debug("同步收码...");
				str = call.receiveDTMFSync(1, "#", 30);
			}
			if ("2".equals(str)) {
				log.debug("挂机...");
				call.onHook();
			}

		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			log.debug("主线程结束。。。。");
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		public TestCase9 mainThread;

		public void setMainThread(TestCase9 mainThread) {
			this.mainThread = mainThread;
		}

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...eventType={},callState={}", event.getType(), event.getCall().getState());
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());

		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase9(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
		}

	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}

}
