/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.ConfEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-14
 */
public class TeleThreadEventListenerDemo implements TeleThreadEventListener {

    private static final Logger log = LoggerFactory.getLogger(TeleThreadEventListenerDemo.class);

    @Override
    public void asyncFinished(BaseEvent event) {
        log.info("in thread listener event come... event={}", event);
        //如果是异步收码结束，关闭收码
        if (event.getType().equals(EventType.RECEIVEDTMF)) {
            ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
            log.debug(" receive dtmf is {} !", ev.getDtmf());
            if ("#".equals(ev.getDtmf())) {
                ev.getCall().stopReceiveDTMF();
            }
        }
        if (event.getType().equals(EventType.LEAVECONF)) {
            log.debug("call is {}", event.getCall());
            ConfEvent conf = (ConfEvent) event;
            int size = CallManager.getInstance().findConfByConfID(conf.getConfID()).getMembers().size();
            log.debug("TeleThreadEventListenerDemo members is {}", size);
        }
     

    }

}
