package com.sungoin.cti.client.demo;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;

public class TestCase16 extends TeleThread {

    private static final Logger LOGGER = LoggerFactory
        .getLogger(TestCase16.class);

    private Call incomeCall;

    private Call agentCallA;

    public TestCase16(Call call, TeleThreadEventListener listener) {
        super(listener);
        this.incomeCall = call;
    }

    @Override
    public void run() {
        try {
            LOGGER.debug("系统应答");
            int ret = this.incomeCall.answer();
            if (ret < 0) {
                this.incomeCall.onHook();
                return;
            }

            LOGGER.debug("同步呼叫坐席：13167062230... ");
            this.agentCallA = this.manager.makeCall("31233490", "13167062230",
                false, 30);
            if (this.agentCallA == null
                || this.agentCallA.getState() != Constants.CALL_CONTENT) {
                this.incomeCall.play(
                    "C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false,
                    false, 10, 5);
                this.incomeCall.onHook();
                return;
            }

            this.incomeCall.setParam("agentCallA", this.agentCallA);
            this.agentCallA.setParam("incomeCall", this.incomeCall);

            this.incomeCall.connectCall(this.agentCallA);
            LOGGER.debug("主叫与坐席建立通话");

            String dtmf = this.agentCallA.receiveDTMFSync(2, "#", 20);
            if ("11".equals(dtmf)) {
                LOGGER.debug("同步收码 11,通话保持");
                this.incomeCall.disconnectCall(this.agentCallA);
                for (int i = 0; i < 2; i++) {
                    dtmf = this.agentCallA.receiveDTMFSync(2, "#", 60);
                    LOGGER.debug("收到的按键:{}", dtmf);
                    if (StringUtils.isEmpty(dtmf)) {
                        if (i == 0) {
                            break;
                        } else {
                            LOGGER.debug("第二次按键错误后,坐席60s内没有按键,通话结束..");
                            this.incomeCall.onHook();
                        }
                    } else if ("12".equals(dtmf)) {
                        LOGGER.debug("收到12按键,通话重新连接");
                        this.incomeCall.connectCall(this.agentCallA);
                        break;
                    } else {

                        //3次按键错误流程结束
                        if (i == 1) {
                            this.incomeCall.onHook();
                            return;
                        }
                        LOGGER.debug("第二次对坐席调用同步收码");
                        dtmf = this.agentCallA.receiveDTMFSync(2, "#", 60);
                        LOGGER.debug("收到的按键:{}", dtmf);

                        if (StringUtils.isEmpty(dtmf)) {
                            LOGGER.debug("第一次按键错误后,坐席60s内没有按键,通话结束..");
                            this.incomeCall.onHook();
                        } else if ("12".equals(dtmf)) {
                            LOGGER.debug("收到12按键,通话重新连接");
                            this.incomeCall.connectCall(this.agentCallA);
                            break;
                        }
                    }
                }
            }
            LOGGER.debug("对坐席异步收码");
            this.agentCallA.receiveDTMFAsync();

        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            LOGGER.debug("主线程结束。。。。");
            this.finished();
        }

    }

    static class MyThreadListener implements TeleThreadEventListener {

        @Override
        public void asyncFinished(BaseEvent event) {
            LOGGER.debug("线程事件...eventType={},callState={}", event.getType(),
                event.getCall().getState());
        }
    }

    static class MyListener implements CTIEventListener {

        @Override
        public void asyncFinished(BaseEvent event) {
            LOGGER.debug("全局事件...type={},call.state={}" + event.getType(),
                event.getCall().getState());

            if (event.getType().equals(EventType.RECEIVEDTMF)) {
                ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
                LOGGER.debug(" receive dtmf is {} !", ev.getDtmf());
                Call call = ev.getCall();
                String key = "dtmf" + call.getCaller();
                if ("#".equals(ev.getDtmf())) {
                    call.stopReceiveDTMF();
                    LOGGER.info("STOP RECEIVE DTMF!");
                    String str = (String) call.getParam(key);
                    LOGGER.debug("坐席按键收码结果为 {}", str);
                    if ("00".equals(str)) {
                        event.getCall().onHook();
                    } else {
                        LOGGER.debug("坐席异步按键收码结果不为00,重新对坐席异步收码,并清除以前按键的码....");
                        call.setParam(key, null);
                        call.receiveDTMFAsync();
                        LOGGER.info("RESTART RECEIVE DEMF!");
                    }
                } else {
                    LOGGER.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(),
                        call.getParam(key));
                    String dtmf = call.getParam(key) != null ? call
                        .getParam(key) + ev.getDtmf() : ev.getDtmf();
                    call.setParam(key, dtmf);
                }
            }

        }

        @Override
        public void callIncome(Call call) {
            LOGGER.info("in global listener callIncome...call={}", call);
            new TestCase16(call, new MyThreadListener()).start();
        }

        @Override
        public void callEnd(Call call) {
            LOGGER.debug("callEnd ...caller={},callee={}", call.getCaller(),
                call.getCallee());
            for (Map.Entry<String, Object> entry : call.getAllParam()
                .entrySet()) {
                Call call1 = (Call) entry.getValue();
                if (call1.getState() == Constants.CALL_CONTENT) {
                    call1.onHook();
                }
            }

        }
    }

    public static void main(String[] args) throws InterruptedException {
        CallManager manager = CallManager.getInstance();
        manager.init(new MyListener());
        LOGGER.debug("等待电话呼入.....");
    }

}
