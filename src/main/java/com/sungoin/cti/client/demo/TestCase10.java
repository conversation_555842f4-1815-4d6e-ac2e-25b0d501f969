/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 1.呼入，输出主叫号码 2.系统摘记，打印摘记事件 3.呼叫坐席A，建立会话 4.异步录音 5.通话转接 6.通话保持 7.重新建立会话
 * 8.添加来电通话参数 9.结束异步录音 10.异步录音 11.结束异步录音 12.对来电挂机
 *
 * <AUTHOR> 2015-5-14
 */
public class TestCase10 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase10.class);
	private final Call call;

	public TestCase10(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：主叫 is {}, 被叫 is {}", call.getCaller(), call.getCallee());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			call.attachTeleThread(this);
			log.debug("对呼入用户同步收码...");
			String str = call.receiveDTMFSync(11, "#", 20);
			log.debug("收码结果为 {}", str);

			log.debug("呼叫坐席A...");
			Call call2 = CallManager.getInstance().makeCall("31233490", str, false, 30);
			if (call2 != null) {
				if (call.getState() != Constants.CALL_CONTENT) {
					log.debug("主叫挂机，主线程退出！");
					call2.onHook();
					return;
				}
				
				log.debug(" 坐席接通，连接通话！");
				call.connectCall(call2);
				log.debug("设置主叫参数，姓名=张三");
				call.setParam("姓名", "张三");
				this.await(1000 * 10);
				call2.onHook();
				log.debug("异步收码...");
				call.receiveDTMFAsync();
			}
			log.debug("主线程结束。。。。");
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			if (event.getType().equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				log.debug(" receive dtmf is {} !", ev.getDtmf());
				Call call = ev.getCall();
				String key = "dtmf" + call.getCaller();
				if ("#".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					log.debug("收码结果为 {}", str);
					call.setParam(key, null);
					if (str.length() == 11) {
						log.debug("开始外呼：{}", str);
						Call call2 = CallManager.getInstance().makeCall("31233490", str, false, 30);
						if (call2 != null) {
							log.debug(" 被叫接通，连接通话...");
							call.connectCall(call2);
							call.setParam("caller", call2);

						}
					} else {
						if ("100".equals(str)) {
							Call call2 = (Call) call.getParam("caller");
							if (call2 != null) {
								log.debug("断开连接...");
								call.disconnectCall(call2);
							}
						} else if ("20".equals(str)) {
							Call call2 = (Call) call.getParam("caller");
							if (call2 != null) {
								log.debug("重新连接...");
								call.connectCall(call2);
							}
						} else if ("007".equals(str)) {
							log.debug("开始录音...");
							call.record("C:\\record\\2.wav", -1, false);
						} else if ("007".equals(str)) {
							log.debug("结束录音...");
							call.stopRecord();
						} else if ("00".equals(str)) {
							log.debug("挂机...");
							call.onHook();
						}
					}
					log.debug("异步收码...");
					call.receiveDTMFAsync();
				} else {
					log.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(), call.getParam(key));
					String dtmf = call.getParam(key) != null ? call.getParam(key) + ev.getDtmf() : ev.getDtmf();
					call.setParam(key, dtmf);
				}
			}
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase10(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
			log.debug("挂机取得参数：姓名={}", call.getParam("姓名"));
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}
}
