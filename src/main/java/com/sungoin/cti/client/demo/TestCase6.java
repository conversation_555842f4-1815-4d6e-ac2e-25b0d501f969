/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TestCase6 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase6.class);
	private final Call call;

	public TestCase6(Call call, TeleThreadEventListener listener) {
		super(listener);
		this.call = call;
	}

	@Override
	public void run() {
		try {
			((MyThreadListener) this.getListener()).setMainThread(this);
			call.setParam("threadName", this.getName());
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：caller is {}, callee is {}, state={}", call.getCaller(), call.getCallee(), call.getState());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			//不是空闲才继续循环
			while (call.getState()!= Constants.CALL_IDLE) {
				//同步收码
				log.debug("对呼入用户同步收码...");
				String str = call.receiveDTMFSync(1, "#", 20);
				log.debug("收码结果为 {}", str);
				if ("1".equals(str)) {
					log.debug("异步放音。。。");
					ret = call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, true, 0, 0);
				} else if ("2".equals(str)) {
					log.debug("停止放音。。。");
					call.stopPlay();
				} else if ("3".equals(str)) {
					log.debug("第一次同步放音5秒。。。");
					call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
					log.debug("第二次同步放音5秒。。。");
					call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
					log.debug("结束同步放音。。。");
				}else if("4".equals(str)){
					log.debug("挂机...");
					call.onHook();
				} 
			}
		}
		catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			log.debug("主线程结束。。。。");
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		public TestCase6 mainThread;

		public void setMainThread(TestCase6 mainThread) {
			this.mainThread = mainThread;
		}

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...eventType={},callState={}", event.getType(), event.getCall().getState());
		}

	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase6(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());

		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}

}
