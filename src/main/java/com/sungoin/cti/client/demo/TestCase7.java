/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TestCase7 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase7.class);
	private final Call call;

	public TestCase7(Call call, TeleThreadEventListener listener) {
		super(listener);
		this.call = call;
	}

	@Override
	public void run() {
		try {
			((MyThreadListener) this.getListener()).setMainThread(this);
			call.setParam("threadName", this.getName());
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：caller is {}, callee is {}, state={}", call.getCaller(), call.getCallee(), call.getState());
			//振铃或摘机
			int ret = call.answerSync(500);
			call.attachTeleThread(this);
			log.debug("对呼入用户摘机...返回：{}", ret);
			//异步收码
			log.debug("对呼入用户异步收码...");
			call.receiveDTMFAsync();

		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			log.debug("主线程结束。。。。");
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		public TestCase7 mainThread;

		public void setMainThread(TestCase7 mainThread) {
			this.mainThread = mainThread;
		}

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...eventType={},callState={}", event.getType(), event.getCall().getState());
		}

	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			if (event.getType().equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				log.debug(" receive dtmf is {} !", ev.getDtmf());
				Call call = ev.getCall();
				String key = "dtmf" + call.getCaller();
				if ("#".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					log.debug("收码结果为 {}", str);
					if ("4".equals(str)) {
						log.debug("挂机...");
						call.onHook();
					} else {
						if ("1".equals(str)) {
							log.debug("异步放音。。。");
							call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, true, 0, 0);
						} else if ("2".equals(str)) {
							log.debug("停止放音。。。");
							call.stopPlay();
						} else if ("3".equals(str)) {
							log.debug("第一次同步放音5秒。。。");
							call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
							log.debug("第二次同步放音5秒。。。");
							call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
							log.debug("结束同步放音。。。");
						}
						log.debug("对呼入用户异步收码...");
						call.receiveDTMFAsync();
						call.setParam(key, null);
					}

				}else if("*".equals(ev.getDtmf())){
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					log.debug("收码结果为 {}", str);
					if(str.length()==3){
						 call.playNum("C:\\DJKeygoe\\Samples\\Voc\\number\\empno.wav", str, "C:\\DJKeygoe\\Samples\\Voc\\number\\service.wav", true, 0);
					}else{
						log.debug("收码不是3位，不做处理！");
					}
					call.receiveDTMFAsync();
					call.setParam(key, null);
				}else {
					log.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(), call.getParam(key));
					String dtmf = call.getParam(key) != null ? call.getParam(key) + ev.getDtmf() : ev.getDtmf();
					call.setParam(key, dtmf);
				}
			}
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase7(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());

		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}

}
