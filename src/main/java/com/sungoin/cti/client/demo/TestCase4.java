/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CONF_MODE;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.Conference;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class TestCase4 extends TeleThread {

    public static final Object sync = new Object();
    private static Conference conf;
    private static final Logger log = LoggerFactory.getLogger(TestCase4.class);

    public TestCase4(TeleThreadEventListener listener) {
        super(listener);
    }

    @Override
    public void run() {
        try {
            String str = "13167062230";
            String str2 = "15214346165";
            log.debug("异步外呼号码为{}", str);
            Call call = manager.makeCall("31233490", str, true, 0);
			call.attachTeleThread(this);
            log.debug("异步外呼号码为{}", str2);
            Call call2 = manager.makeCall("31233490", str2, true, 0);
			call2.attachTeleThread(this);
            log.debug("call设置参数为：name=坐席A");
            log.debug("call2设置参数为：name=坐席B");
            call.setParam("name", "坐席A");
            call2.setParam("name", "坐席B");
			log.debug("2分钟后将第二个接电话的坐席挂机！");
			this.await(1000 *60 *2);
			log.debug("2分钟时间到！");
			Boolean isOnhook = call.getParam("onhook") ==null ?false :Boolean.parseBoolean(call.getParam("onhook")+"")   ;
			Call onhookCall =null;
			Call levaeCall=null;
			if((isOnhook)){
				onhookCall=call;
				levaeCall =call2;
			}else {
				onhookCall=call2;
				levaeCall =call;
			}
			log.debug(onhookCall+"是第二个加入会议的。。。");
			onhookCall.onHook();
			log.debug("30秒后将还存在的用户挂机！");
			this.await(1000 *30);
			levaeCall.onHook();
        } catch (Exception ex) {
           log.error(ex.getMessage(),ex);
        } finally {
            //import clean method.
            this.finished();
        }
    }

    static class MyThreadListener implements TeleThreadEventListener {
		
        @Override
        public void asyncFinished(BaseEvent event) {
            log.debug("线程事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			 String eventType = event.getType();
            if (eventType.equals(EventType.JOINCONF)) {
                   log.debug(event.getCall().getCallee()+"加入会议。。。");
            } else if (eventType.equals(EventType.LEAVECONF)) {
                log.debug(event.getCall().getCallee()+"离开会议。。。");
            } else if (eventType.equals(EventType.CLEARCONF)) {
                log.debug("会议结束。。。");
            } else  if (event.getType().equals(EventType.CALLOUT)) {
                log.debug("{}异步外呼结果：{}", event.getCall().getCallee(), event.getEventState());
                if (event.getEventState() == Constants.RET_SUCCESS) {
                    Call call = event.getCall();
                    synchronized (TestCase4.sync) {
                        log.debug("conf {}", conf);
                        if (conf == null) {
//                            call.createConf(CONF_MODE.ADD);
//                            try {
//                                Thread.sleep(500);
//                            } catch (InterruptedException ex) {
//                                log.error(ex.getMessage(),ex);
//                            }
							call.createConfSync(CONF_MODE.ADD, 500);
                            conf = call.getCurrentConf();
                        } else {
                            call.joinConf(conf, CONF_MODE.ADD);
							 call.setParam("onhook", true);
                        }
                    }

                }
            }
        }
    }

    static class MyListener implements CTIEventListener {

        @Override
        public void asyncFinished(BaseEvent event) {
            log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
           
        }

        @Override
        public void callIncome(Call call) {
            log.info("in global listener callIncome...call={}", call);
        }

        @Override
        public void callEnd(Call call) {
            log.debug("callEnd ...");
          
            log.debug("挂机取得参数：姓名={}", call.getParam("name"));
        }
    }

    public static void main(String[] args) throws InterruptedException {
        CallManager manager = CallManager.getInstance();
        manager.init(new MyListener());
        Thread.sleep(2000);
        log.debug("================ start make call");
        TestCase4 ct = new TestCase4(new MyThreadListener());
        ct.start();
    }

}
