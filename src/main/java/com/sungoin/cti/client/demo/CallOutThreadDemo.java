/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class CallOutThreadDemo extends TeleThread {

    private static final Logger log = LoggerFactory.getLogger(CallOutThreadDemo.class);

    public CallOutThreadDemo(TeleThreadEventListener listener) {
        super(listener);
    }

    @Override
    public void run() {
        try {
            Call call = manager.makeCall("31233490", "13701836419", false, 30 * 1000);//同步呼叫
            if (call != null) {
				Call call2 = manager.makeCall("31233490", "13651646619", true, 0);//异步呼叫
				//关联呼叫（在呼叫中关联主被叫）
				call.connectCall(call2);
				call2.setParam("mainCall", call);
				log.info("关联呼叫开始...");
				//呼通后需要再次连接两通呼叫，否则被叫听不到主叫声音
            } else {
                log.warn("呼叫主叫失败！");
            }
            log.info("thread quit...");
        } catch (Exception ex) {
            log.error(ex.getMessage(),ex);
        } finally {
            //import clean method.
            this.finished();
        }
    }
	
	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.info("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			if(event.getType().equals(EventType.CALLOUT) && event.getEventState() == 1) {
				Call call2 = event.getCall();
				Call mainCall = (Call) call2.getParam("mainCall");
				call2.connectCall(mainCall);
				log.info("connect call success!");
			}
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
		}
	}

    public static void main(String[] args) throws InterruptedException {
        CallManager manager = CallManager.getInstance();
        manager.init(new MyListener());
        Thread.sleep(2000);
        log.debug("================ start make call");
        CallOutThreadDemo ct = new CallOutThreadDemo(new TeleThreadEventListener() {
			@Override
			public void asyncFinished(BaseEvent event) {
				log.info("线程事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			}
		});
        ct.start();
    }

}
