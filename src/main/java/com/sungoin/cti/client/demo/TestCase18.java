package com.sungoin.cti.client.demo;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import com.sungoin.cti.client.exception.TimeoutException;
import java.util.ArrayList;
import java.util.List;

public class TestCase18 extends TeleThread {

	private static final Logger LOGGER = LoggerFactory.getLogger(TestCase18.class);

	private Call incomeCall;

	private Call agentCallA;

	private static TestCase18 test18;

	private final List<Call> callIn = new ArrayList<Call>();

	public TestCase18(Call call, TeleThreadEventListener listener) {
		super(listener);
		this.incomeCall = call;
	}

	public static void setMainThread(TestCase18 mainThread) {
		test18 = mainThread;
	}

	@Override
	public void run() {
		try {
			setMainThread(this);

			LOGGER.debug("系统应答");
			int ret = this.incomeCall.answer();
			if (ret < 0) {
				this.incomeCall.onHook();
				return;
			}
			LOGGER.debug("异步呼叫坐席：15000304023... ");
			agentCallA = this.manager.makeCall("31233490", "13651646619", true, 0);
			agentCallA.setParam("agent", incomeCall);
			this.await(1000 * 30);
			LOGGER.debug("agentCallA is state {}", agentCallA.getState());
			if (agentCallA.getState() == Constants.CALL_ING) {
				LOGGER.debug("停止呼叫当前坐席，开始呼叫下一个坐席... ");
				 this.manager.stopMakeCall(agentCallA.getDeviceId(), agentCallA.getLsh());
				this.await(2000);
				agentCallA = this.manager.makeCall("31233490", "15214346165", true, 0);
				agentCallA.setParam("agent", agentCallA);
				this.await(1000 * 30);
				if (agentCallA.getState() == Constants.CALL_ING) {
					CallManager.getInstance().stopMakeCall(agentCallA.getDeviceId(), agentCallA.getLsh());
					LOGGER.debug("停止呼叫坐席2... ");
				}
			}

		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
		} finally {
			LOGGER.debug("主线程结束。。。。");
			this.finished();
		}

	}

	static class MyThreadListener implements TeleThreadEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			LOGGER.debug("线程事件...eventType={},callState={}", event.getType(),
					event.getCall().getState());
			Call call = event.getCall();
			if (event.getType().equals(EventType.ONHOOK)) {
				CallManager.getInstance().stopMakeCall(test18.agentCallA.getDeviceId(), test18.agentCallA.getLsh());
			} else if (event.getType().equals(EventType.CALLOUT)) {
				LOGGER.debug("{}异步外呼结果：{}", event.getCall().getCallee(), event.getEventState());
				if (event.getEventState() == Constants.RET_SUCCESS) {
					LOGGER.debug("坐席接通！");
					call.connectCall(test18.incomeCall);
					LOGGER.debug("坐席{}异步收码！",test18.agentCallA.getCallee());
					test18.agentCallA.receiveDTMFAsync();
				}
			} else if (event.getType().equals(EventType.RECEIVEDTMF)) {
				receiveDTMF(event);
			}
		}
	}

	private static void receiveDTMF(BaseEvent event) {
		Call call = event.getCall();
		ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
		LOGGER.debug(" receive dtmf is {} !", ev.getDtmf());
		String key = "dtmf" + call.getCaller();
		if ("#".equals(ev.getDtmf())) { 
			call.stopReceiveDTMF();
			String str = (String) call.getParam(key);
			Call call2 = (Call) call.getParam("agent");
			LOGGER.debug("坐席按键收码结果为 {}", str);
			if (str.length() == 11) {
				LOGGER.debug("呼叫坐席...");
				Call call3 = CallManager.getInstance().makeCall("31233490", str, false, 0);
				if (call3 == null) {
					LOGGER.debug(" 转接坐席未接通！");
					call.onHook();
					call2.onHook();
				} else {
					LOGGER.debug(" 收码坐席接通...");
					test18.agentCallA = call3;
					test18.agentCallA.setParam("agent", call2);
					call.disconnectCall(call2);
					call2.connectCall(call3);
					call.onHook();
					call3.receiveDTMFAsync();
				}
			} else {
				if ("00".equals(str)) {
					call.onHook();
					call2.onHook();
				} else if ("11".equals(str)) {
					LOGGER.debug("通话保持");
					call.disconnectCall(call2);
				} else if ("12".equals(str)) {
					LOGGER.debug("连接通话... ");
					call.connectCall(call2);
				}
				call.setParam(key, null);
				call.receiveDTMFAsync();

			}

		} else {
			LOGGER.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(),
					call.getParam(key));
			String dtmf = call.getParam(key) != null ? call
					.getParam(key) + ev.getDtmf() : ev.getDtmf();
			call.setParam(key, dtmf);
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			LOGGER.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			Call call = event.getCall();
			if (event.getType().equals(EventType.RECEIVEDTMF)) {
				receiveDTMF(event);
			}

		}

		@Override
		public void callIncome(Call call) {
			LOGGER.info("in global listener callIncome...call={}", call);
			new TestCase18(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			LOGGER.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
			if (test18.incomeCall.equals(call) && test18.agentCallA.getState() == Constants.CALL_ING) {
				LOGGER.debug("主叫挂机，取消呼叫...");
				CallManager.getInstance().stopMakeCall(test18.agentCallA.getDeviceId(), test18.agentCallA.getLsh());
			}
			//只有是正在通话的2个坐席挂断才结束通话
			if (test18.incomeCall.equals(call) || test18.agentCallA.equals(call)) {
				test18.incomeCall.onHook();
				test18.agentCallA.onHook();
			}

		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		LOGGER.debug("等待电话呼入.....");
	}

}
