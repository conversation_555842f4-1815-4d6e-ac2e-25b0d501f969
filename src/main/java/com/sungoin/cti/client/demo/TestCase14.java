/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class TestCase14 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase14.class);

	public TestCase14(TeleThreadEventListener listener) {
		super(listener);
	}

	@Override
	public void run() {
		try {
			log.debug("呼叫13167062230...");
			Call call = manager.makeCall("31233490", "13167062230", false, 30 );
			if (call != null) {
				log.debug("对呼入用户同步收码...");
				String str = call.receiveDTMFSync(11, "#", 20);
				log.debug("收码结果为：{}", str);
				Call call2 = manager.makeCall("31233490", str, false, 30 );
				if (call2 != null) {
					call2.connectCall(call);
					log.debug("对呼入用户同步收码...");
					str = call.receiveDTMFSync(2, "#", 20);
					if ("00".equals(str)) {
						log.debug("断开连接...");
						call.disconnectCall(call2);
					}
					log.debug("对呼入用户同步收码...");
					str = call.receiveDTMFSync(2, "#", 20);
					if ("11".equals(str)) {
						log.debug("断开连接...");
						call.connectCall(call2);
					}
					log.debug("对呼入用户同步收码...");
					str = call.receiveDTMFSync(2, "#", 20);
					if ("12".equals(str)) {
						log.debug("开始录音...");
						call.record("C:\\record\\1.wav", -1, false);
					}
					log.debug("对呼入用户同步收码...");
					str = call.receiveDTMFSync(2, "#", 20);
					if ("01".equals(str)) {
						log.debug("挂机...");
						call.onHook();
						call2.onHook();
					}
				}

			}
			log.debug(" 主线程结束...");
		} catch (Exception ex) {
			ex.printStackTrace();
		} finally {
			//import clean method.
			this.finished();
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new CTIEventListenerDemo());
		Thread.sleep(2000);
		log.debug("================ start make call");
		TestCase14 ct = new TestCase14(new TeleThreadEventListenerDemo());
		ct.start();
	}

}
