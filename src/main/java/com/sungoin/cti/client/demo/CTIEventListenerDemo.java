/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.event.BaseEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-14
 */
public class CTIEventListenerDemo implements CTIEventListener {

	private static final Logger log = LoggerFactory.getLogger(CTIEventListenerDemo.class);

	@Override
	public void callIncome(Call call) {
		log.info("in global listener callIncome...call={}",call);
		new TestCase1(call, new TeleThreadEventListenerDemo()).start();
	}

	@Override
	public void callEnd(final Call call) {
		log.info("in global listener callEnd...call={}",call);
	}

	@Override
	public void asyncFinished(BaseEvent event) {
		log.info("in global listener event come... event={}", event);
	}

}
