package com.sungoin.cti.client.demo;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import static com.sungoin.cti.client.demo.TestCase18.setMainThread;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import com.sungoin.cti.client.exception.TimeoutException;
import java.util.ArrayList;
import java.util.List;

public class TestCase17 extends TeleThread {

	private static final Logger LOGGER = LoggerFactory.getLogger(TestCase17.class);

	private Call incomeCall;

	private Call agentCallA;

	private static TestCase17 test17;

	private final List<Call> callIn = new ArrayList<Call>();

	public TestCase17(Call call, TeleThreadEventListener listener) {
		super(listener);
		this.incomeCall = call;
	}

	public static void setMainThread(TestCase17 mainThread) {
		test17 = mainThread;
	}

	@Override
	public void run() {
		try {
			setMainThread(this);

			LOGGER.debug("系统应答");
			int ret = this.incomeCall.answer();
			if (ret < 0) {
				this.incomeCall.onHook();
				return;
			}
			LOGGER.debug("异步呼叫坐席：15000304023... ");
			this.agentCallA = this.manager.makeCall("31233490", "15000304023",
					true, 0);
			incomeCall.setParam("agent", agentCallA);
			agentCallA.setParam("agent", incomeCall);
			this.await(1000 * 30);
			LOGGER.debug("agentCallA is state {}", agentCallA.getState());
			if (agentCallA.getState() == Constants.CALL_ING) {
				LOGGER.debug("停止呼叫坐席,进入留言,开始录音...");
				LOGGER.debug("坐席未接进入留言,放留言音...");
				CallManager.getInstance().stopMakeCall(agentCallA.getDeviceId(), agentCallA.getLsh());
				incomeCall.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\2012012108595.wav", false, false, false, 0, 27);

				incomeCall.record("C:\\record\\1.wav", -1, false);
			}

		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
		} finally {
			LOGGER.debug("主线程结束。。。。");
			this.finished();
		}

	}

	static class MyThreadListener implements TeleThreadEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			LOGGER.debug("线程事件...eventType={},callState={}", event.getType(),
					event.getCall().getState());
			Call call = event.getCall();
			if (event.getType().equals(EventType.ONHOOK)) {
				LOGGER.debug(" 主叫挂机...取消呼叫...");
				CallManager.getInstance().stopMakeCall(test17.agentCallA.getDeviceId(), test17.agentCallA.getLsh());
			} else if (event.getType().equals(EventType.CALLOUT)) {
				LOGGER.debug("{}异步外呼结果：{}", event.getCall().getCallee(), event.getEventState());
				if (event.getEventState() == Constants.RET_SUCCESS) {
					LOGGER.debug("坐席接通！");
					call.connectCall(test17.incomeCall);
					LOGGER.debug("主叫/坐席异步收码！");
					test17.incomeCall.receiveDTMFAsync();
					test17.agentCallA.receiveDTMFAsync();
				}
			} else if (event.getType().equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				LOGGER.debug(" receive dtmf is {} !", ev.getDtmf());
				String key = "dtmf" + call.getCaller();
				if ("#".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					Call call2 = (Call) call.getParam("agent");
					LOGGER.debug("坐席按键收码结果为 {}", str);
					if ("00".equals(str)) {
						call.onHook();
						call2.onHook();
					} else if ("11".equals(str)) {
						LOGGER.debug("通话保持");
						call.disconnectCall(call2);
					} else if ("12".equals(str)) {
						LOGGER.debug("连接通话... ");
						call.connectCall(call2);
					}
					call.setParam(key, null);
					call.receiveDTMFAsync();
				} else {
					LOGGER.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(),
							call.getParam(key));
					String dtmf = call.getParam(key) != null ? call
							.getParam(key) + ev.getDtmf() : ev.getDtmf();
					call.setParam(key, dtmf);
				}
			}
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			LOGGER.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			Call call = event.getCall();
			if (event.getType().equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				LOGGER.debug(" receive dtmf is {} !", ev.getDtmf());
				String key = "dtmf" + call.getCaller();
				if ("#".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					Call call2 = (Call) call.getParam("agent");
					LOGGER.debug("坐席按键收码结果为 {}", str);
					if ("00".equals(str)) {
						call.onHook();
						call2.onHook();
					} else if ("11".equals(str)) {
						LOGGER.debug("通话保持");
						call.disconnectCall(call2);
					} else if ("12".equals(str)) {
						LOGGER.debug("连接通话... ");
						call.connectCall(call2);
					}
					call.setParam(key, null);
					call.receiveDTMFAsync();
				} else {
					LOGGER.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(),
							call.getParam(key));
					String dtmf = call.getParam(key) != null ? call
							.getParam(key) + ev.getDtmf() : ev.getDtmf();
					call.setParam(key, dtmf);
				}
			}

		}

		@Override
		public void callIncome(Call call) {
			LOGGER.info("in global listener callIncome...call={}", call);
			new TestCase17(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			LOGGER.debug("callEnd ...caller={},callee={}", call.getCaller(),
					call.getCallee());
			LOGGER.debug("  incomeCall state ...{}",test17.incomeCall.getState()," agentCallA state ...{}",test17.agentCallA.getState());
			if (test17.incomeCall.equals(call) && test17.agentCallA.getState() == Constants.CALL_ING) {
				LOGGER.debug("主叫挂机，取消呼叫...");
				CallManager.getInstance().stopMakeCall(test17.agentCallA.getDeviceId(), test17.agentCallA.getLsh());
			}
			test17.agentCallA.onHook();
			test17.incomeCall.onHook();
			
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		LOGGER.debug("等待电话呼入.....");
	}

}
