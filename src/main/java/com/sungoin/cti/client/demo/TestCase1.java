/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 1.呼入，输出主叫号码 2.系统摘记，打印摘记事件 3.同步收码，超时时间为20s，以#号结束 4.如收到（12）码，异步放音 5.同步收码，以#号结束
 * 6.如收到（34）码，停止放音，否则直到放音结束 7.同步收码，以#号结束 8.如收到（78）码，同步循环放音2次，每次放音5秒
 * 9.异步收码，满足4位，以*号结束 10.报工号、 11.同步收码，以#号结束 12.如收到（00）码，挂机
 *
 * <AUTHOR> 2015-5-14
 */
public class TestCase1 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase1.class);
	private final Call call;
	public final Object sync = new Object();
	public boolean isWaiting = false;
	public boolean numberReceived = false;
	public String number;

	public TestCase1(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			((MyThreadListener) this.getListener()).setMainThread(this);
			call.setParam("threadName", this.getName());
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：caller is {}, callee is {}, state={}", call.getCaller(), call.getCallee(), call.getState());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			//等待call状态改变
//			while(call.getState() != Constants.CALL_CONTENT) {
//				this.await(200);
//				if(call.getState() == Constants.CALL_IDLE) {
//					return;
//				}
//			}
			//同步收码
//			log.debug("对呼入用户同步收码...");
//			String str = call.receiveDTMFSync(2, "#", 20);
//			log.debug("收码结果为 {}", str);
//			if (!"12".equals(str)) {
//				return;
//			}
//			log.debug("开始异步放音 : {}", ret);
//			ret = call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, true, 0, 0);
//
//			log.debug("放音同时对呼入用户第二次同步收码...");
//			str = call.receiveDTMFSync(2, "#", 20);
//			log.debug("收码结果为 {}", str);
//			if (!("34".equals(str))) {
//				return;
//			}
//			//停止异步放音
//			call.stopPlay();
//			log.debug("对呼入用户第三次同步收码...");
//			str = call.receiveDTMFSync(2, "#", 20);
//			log.debug("收码结果为 {}", str);
//			if ("78".equals(str)) {
//				log.debug("第一次同步放音5秒。。。");
//				call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
//				log.debug("第二次同步放音5秒。。。");
//				call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
//				log.debug("结束同步放音。。。");
//
//				log.debug(" 开始异步收码。。。");
//				call.receiveDTMFAsync();
//				//等待异步收码完成后唤醒主线程执行同步任务
//				synchronized (sync) {
//					while (!numberReceived) {
//						isWaiting = true;
//						sync.wait();
//						log.debug("主线程被唤醒...");
//					}
//					isWaiting = false;
//					if (call.getState() != Constants.CALL_CONTENT) {
//						log.debug("检测到挂机，主线程结束。。。。");
//						return;
//					}
//				}
//				//唤醒后执行的同步任务
//				call.playNum("C:\\DJKeygoe\\Samples\\Voc\\number\\empno.wav", number, "C:\\DJKeygoe\\Samples\\Voc\\number\\service.wav", false, 30);
//				log.debug("报工号：{}结束", number);
//				if (call.getState() != Constants.CALL_CONTENT) {
//					log.debug("检测到挂机，主线程结束。。。。");
//					return;
//				}
//				log.debug("同步收码...");
//				str = call.receiveDTMFSync(2, "#", 20);
//				log.debug("收码结果为 {}", str);
//				if (call.getState() != Constants.CALL_CONTENT) {
//					log.debug("检测到挂机，主线程结束。。。。");
//					return;
//				}
//				if ("00".equals(str)) {
//					log.debug("挂机。。。");
//					call.onHook();
//				}
//			}
			Call call2 = this.manager.makeCall("31233490", "13701836419", false, 30);
			if(call2 != null) {
				call.connectCall(call2);
			}
			this.await(5000);
			call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 0, 0);
//			call.playNum("f:/data/voices/default/empno.wav", "2006", "f:/data/voices/default/service.wav", false, 0);
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			log.debug("主线程结束。。。。");
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		public TestCase1 mainThread;

		public void setMainThread(TestCase1 mainThread) {
			this.mainThread = mainThread;
		}

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...eventType={},callState={}", event.getType(), event.getCall().getState());
			if (event.getType().equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				log.debug(" receive dtmf is {} !", ev.getDtmf());
				Call call = ev.getCall();
				String key = "dtmf" + call.getCaller();
				if ("*".equals(ev.getDtmf())) {
					call.stopReceiveDTMF();
					String str = (String) call.getParam(key);
					log.debug("收码结果为 {}", str);
					//对主线程赋值，唤醒主线程
					synchronized (mainThread.sync) {
						mainThread.number = str;
						mainThread.numberReceived = true;
						mainThread.sync.notifyAll();
					}
				} else {
					log.debug("收到码为:{},上次储存的码为:{}", ev.getDtmf(), call.getParam(key));
					String dtmf = call.getParam(key) != null ? call.getParam(key) + ev.getDtmf() : ev.getDtmf();
					call.setParam(key, dtmf);
				}
			}
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase1(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
			String threadName = call.getParam("threadName").toString();
			TeleThread teleThread = CallManager.getInstance().getTeleThread(threadName);
			log.debug("callEnd  ... teleThread is {}", teleThread);
			if (teleThread != null) {
				TestCase1 t1 = (TestCase1) teleThread;
				if (t1.isWaiting) {
					synchronized (t1.sync) {
						t1.numberReceived = true;
						t1.sync.notifyAll();
					}
				}
			}
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}
}
