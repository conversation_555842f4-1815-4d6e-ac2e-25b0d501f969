/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-6-23 在
 */
public class EventSyncDemo extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(EventSyncDemo.class);
	private final Call call;

	public EventSyncDemo(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			log.debug("开始呼入线程...");
			call.attachTeleThread(this);
			call.answerSync(500);
			call.play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
			int ret = call.receiveDTMFAsync();
			log.debug("开始异步收码,#号键结束", ret);
			log.info("主线程结束");
		} catch (Exception ex) {
		} finally {
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		public TestCase1 mainThread;

		public void setMainThread(TestCase1 mainThread) {
			this.mainThread = mainThread;
		}

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...eventType={},callState={}", event.getType(), event.getCall().getState());
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			if (event.getType().equals(EventType.RECEIVEDTMF)) {
				ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
				log.debug(" receive dtmf is {} !", ev.getDtmf());
				Call call = event.getCall();
//				if (ev.getDtmf().equals("#")) {
//					log.debug("收到#号符，开始同步放音5秒");
//					call.stopReceiveDTMF();
//					event.getCall().play("C:\\DJKeygoe\\Samples\\Voc\\voice\\1.wav", false, false, false, 10, 5);
//					log.debug("同步放音结束");
//					log.debug("开始同步收码。。。");
//					String str = call.receiveDTMFSync(2, "#", 20);
//					log.debug("收码结果为 {}", str);
//					log.info("业务结束...等待挂机！");
//				}
			}
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new EventSyncDemo(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}
}
