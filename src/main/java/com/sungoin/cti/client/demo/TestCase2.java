/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 1.呼入，输出主叫号码 2.系统摘记，打印摘记事件 3.呼叫坐席A，建立会话 4.异步录音 5.通话转接 6.通话保持 7.重新建立会话
 * 8.添加来电通话参数 9.结束异步录音 10.异步录音 11.结束异步录音 12.对来电挂机
 *
 * <AUTHOR> 2015-5-14
 */
public class TestCase2 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase2.class);
	private final Call call;

	public TestCase2(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：主叫 is {}, 被叫 is {}", call.getCaller(), call.getCallee());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			
			log.debug("对呼入用户同步收码...");
			String str = call.receiveDTMFSync(11, "#", 20);
			log.debug("收码结果为 {}", str);
			
			log.debug("呼叫坐席A...");
			Call call2 = CallManager.getInstance().makeCall("31233490", str, false, 30);
			if (call2 != null) {
				if (call.getState() != Constants.CALL_CONTENT) {
					log.debug("主叫挂机，主线程退出！");
					call2.onHook();
					return;
				}
				log.debug(" 坐席接通，连接通话！");
				call.connectCall(call2);
				log.debug("设置主叫参数，姓名=张三");
				call.setParam("姓名", "张三");
				log.debug("开始异步录音...");
				call.record("C:\\record\\1.wav", -1, false);
				this.await(5000);
				log.debug("开始呼叫坐席B...");
				Call call3 = CallManager.getInstance().makeCall("31233490", "13167062230", false, 30);
				if (call3 != null) {
					if (call.getState() != Constants.CALL_CONTENT) {
						log.debug("主叫挂机，主线程退出！");
						call3.onHook();
						return;
					}
					log.debug(" 坐席B接通，将主叫和坐席A断开连接。。。 ");
					call.disconnectCall(call2);
					log.debug(" 将坐席A挂机");
					call2.onHook();
					log.debug(" 将主叫与坐席B连接");
					call.connectCall(call3);
					this.await(5000);
					log.debug("通话5秒后，断开连接(保持)60秒");
					call.disconnectCall(call3);
					this.await(60000);
					log.debug("接回...");
					call.connectCall(call3);
					call.stopRecord();
					this.await(5000);
					log.debug("开始第二次录音");
					call.record("C:\\record\\2.wav", -1, false);
					this.await(5000);
					log.debug("结束异步录音。。。");
					call.stopRecord();
					log.debug("挂机。。。");
					call.onHook();
					call3.onHook();
				}
			}
			log.debug("主线程结束。。。。");
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...type={},call.state={}" + event.getType(), event.getCall().getState());
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase2(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
			log.debug("挂机取得参数：姓名={}", call.getParam("姓名"));
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}
}
