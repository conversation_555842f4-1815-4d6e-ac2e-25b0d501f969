/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CONF_MODE;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.Conference;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 1.呼入，输出主叫号码 2.系统摘记，打印摘记事件 3.同步收码，收电话号码，以#号结束 4. 建立通话 5. 创建会议,将之前的通话加入
 *
 * <AUTHOR> 2015-5-14
 */
public class TestCase3 extends TeleThread {

	private static final Logger log = LoggerFactory.getLogger(TestCase3.class);
	private final Call call;

	public TestCase3(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：主叫 is {}, 被叫 is {}", call.getCaller(), call.getCallee());
			//振铃或摘机
			log.debug("对呼入用户摘记...");
			int ret = call.answerSync(200);

			String str = call.receiveDTMFSync(11, "#", 30);
			log.debug("收码结果为：{} ，开始呼叫{}", str, str);
			Call call2 = CallManager.getInstance().makeCall("31233490", str, false, 30);
			if (call2 != null) {
				log.debug(" 坐席接通，连接通话！");
				if (call.getState() != Constants.CALL_CONTENT) {
					log.debug("主叫已挂机!");
					call2.onHook();
					return;
				}
				call.connectCall(call2);

				this.await(5000);
				if (call.getState() != Constants.CALL_CONTENT) {
					log.debug("主叫已挂机!");
					call2.onHook();
					return;
				}
				call.disconnectCall(call2);
				log.debug("开始创建会议...");
				ret = call.createConfSync(CONF_MODE.ADD, 300);
				if (call.getState() != Constants.CALL_CONTENT) {
					log.debug("主叫已挂机!");
					return;
				}
				log.debug(" 创建会议结果：{}", ret);
				Conference conf = call.getCurrentConf();
				log.debug("坐席A加入会议");
				call2.joinConf(conf, CONF_MODE.ADD);
				log.debug("同步外呼坐席B");
				Call call3 = CallManager.getInstance().makeCall("31233490", "13167062230", false, 30);
				if (call3 != null) {
					log.debug(" 坐席B以只听模式加入会议 ...");
					call3.joinConf(conf, CONF_MODE.LISTEN);
				}
				//异步呼叫
				log.debug("异步呼叫。。。");
				Call call4 = manager.makeCall("31233490", "18651335972", true, 60 * 1000);
				call4.setParam("conf", conf);
				if (call.getState() != Constants.CALL_CONTENT) {
					log.debug("主叫已挂机!");
					return;
				}
				//开始收码
				str = call.receiveDTMFSync(2, "#", 60 * 5);
				log.debug("收码结果为{}", str);

				if ("01".equals(str)) {
					log.debug(" 对主叫录音...");
					if (call.getState() != Constants.CALL_CONTENT) {
						log.debug("主叫已挂机!");
						return;
					}
					call.record("C:\\record\\2.wav", -1, false);
				}
				if (call.getState() != Constants.CALL_CONTENT) {
					log.debug("主叫已挂机!");
					return;
				}
				call.onHook();
				call2.onHook();
				call3.onHook();
				call4.onHook();
			}
			log.debug("主线程结束。。。。");
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			this.finished();
		}
	}

	static class MyThreadListener implements TeleThreadEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			doWork(event, "线程事件");
		}
	}

	static class MyListener implements CTIEventListener {

		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
			doWork(event, "全局事件");

		}

		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase3(call, new MyThreadListener()).start();
		}

		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
		}
	}

	static void doWork(BaseEvent event, String name) {
		log.debug(name);
		String eventType = event.getType();
		if (eventType.equals(EventType.JOINCONF)) {
			log.debug(event.getCall().getCallee() + "加入会议。。。");
		} else if (eventType.equals(EventType.LEAVECONF)) {
			log.debug(event.getCall().getCallee() + "离开会议。。。");
		} else if (eventType.equals(EventType.CLEARCONF)) {
			log.debug("会议结束。。。");
		} else if (event.getType().equals(EventType.RECORDEND)) {
			log.debug("录音结束。。。");
		} else if (event.getType().equals(EventType.CALLOUT)) {
			log.debug("{}异步外呼结果：{}", event.getCall().getCallee(), event.getEventState());
			if (event.getEventState() == Constants.RET_SUCCESS) {
				Call call = event.getCall();
				Conference conf = (Conference) call.getParam("conf");
				if (conf != null) {
					log.debug("坐席以只说模式加入会议！");
					event.getCall().joinConf(conf, CONF_MODE.SPEAKONLY);
				}

			}
		}
	}

	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}
}
