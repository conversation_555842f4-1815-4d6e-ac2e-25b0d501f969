/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CONF_MODE;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.Conference;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-14
 */
public class ConfDemo extends TeleThread {

    private static final Logger log = LoggerFactory.getLogger(ConfDemo.class);

    public ConfDemo(TeleThreadEventListener listener) {
        super(listener);
    }

    @Override
    public void run() {
        try {
            Call call = manager.makeCall("31233490", "13651646619", false, 60 * 1000);
            if (call != null) {
                call.createConf(CONF_MODE.ADD);
                this.await(500);

                Conference conf = call.getCurrentConf();
                log.info("first call joined, conf count " + conf.getMembers().size());

                //同步呼叫
//                Call call2 = manager.makeCall("31233490", "13661636379", false, 60 * 1000);
//                if (call2 != null) {
//                    call2.joinConf(conf, CONF_MODE.ADD);
//                    this.await(500);
//                    log.info("second call joined, conf count " + conf.getMembers().size());
//                    Call call3 = manager.makeCall("31233490", "13701836419", false, 60 * 1000);
//                    if (call3 != null) {
//                        call3.joinConf(conf, CONF_MODE.ADD);
//						this.await(500);
//						log.info("third call joined, conf count " + conf.getMembers().size());
//						call.record("c:/1.wav", -1, false);
////                        this.await(5000);
////						int ret = call3.leaveConf();
////						log.info("remove third call from conference...ret={}",ret);
////						await(500);
////						log.info("third call removed, conf count " + conf.getMembers().size());
//                    }
//                }
                //异步呼叫
                ((MyListener) this.getListener()).setConf(conf);
                Call call2 = manager.makeCall("31233490", "13661636379", true, 60 * 1000);
                Call call3 = manager.makeCall("31233490", "13701836419", true, 60 * 1000);
            }
            this.await(20000);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        } finally {
            //import clean method.
            this.finished();
        }
    }

    static class MyListener implements TeleThreadEventListener {

        private Conference conf;

        public void setConf(Conference conf) {
            this.conf = conf;
        }

        @Override
        public void asyncFinished(BaseEvent event) {
            if (event.getType().equals(EventType.CALLOUT)) {
                event.getCall().joinConf(conf, CONF_MODE.ADD);
            }
        }

    }

    public static void main(String[] args) throws InterruptedException {
        CallManager manager = CallManager.getInstance();
        manager.init(new CTIEventListenerDemo());
        Thread.sleep(2000);
        log.debug("================ start make call");
        ConfDemo ct = new ConfDemo(new MyListener());
        ct.start();
    }
}
