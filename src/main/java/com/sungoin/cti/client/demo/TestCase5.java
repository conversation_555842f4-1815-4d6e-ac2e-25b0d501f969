/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.demo;

import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TestCase5 extends TeleThread {
	
	private static final Logger log = LoggerFactory.getLogger(TestCase5.class);
	private final Call call;
	
	public TestCase5(Call callin, TeleThreadEventListener listener) {
		super(listener);
		this.call = callin;
	}

	@Override
	public void run() {
		try {
			((MyThreadListener) this.getListener()).setMainThread(this);
			call.setParam("threadName", this.getName());
			log.debug("开始呼入线程...");
			//业务操作
			log.info("主叫呼入：caller is {}, callee is {}, state={}", call.getCaller(), call.getCallee(), call.getState());
			//振铃或摘机
			int ret = call.answerSync(500);
			log.debug("对呼入用户摘机...返回：{}", ret);
			log.debug("主叫号码为：{}", call.getCaller());
			if ("31233188".equals(call.getCaller())) {
				CallManager.getInstance().makeCall("31233490", "15214346165", false, 30);
			} else if ("13167062230".equals(call.getCaller())) {
				Call call2 = CallManager.getInstance().makeCall("31233490", "15214346165", false, 30);
				if (call2 == null) {
					call.onHook();
				}
			} else {
				log.debug("其他号码不做处理。。。");
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		} finally {
			log.debug("主线程结束。。。。");
			this.finished();
		}
	}
	
	static class MyThreadListener implements TeleThreadEventListener {
		
		public TestCase5 mainThread;
		
		public void setMainThread(TestCase5 mainThread) {
			this.mainThread = mainThread;
		}
		
		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("线程事件...eventType={},callState={}", event.getType(), event.getCall().getState());
		}
	}
	
	static class MyListener implements CTIEventListener {
		
		@Override
		public void asyncFinished(BaseEvent event) {
			log.debug("全局事件...type={},call.state={}" + event.getType(), event.getCall().getState());
		}
		
		@Override
		public void callIncome(Call call) {
			log.info("in global listener callIncome...call={}", call);
			new TestCase5(call, new MyThreadListener()).start();
		}
		
		@Override
		public void callEnd(Call call) {
			log.debug("callEnd ...caller={},callee={}", call.getCaller(), call.getCallee());
		}
		
	}
	
	public static void main(String[] args) throws InterruptedException {
		CallManager manager = CallManager.getInstance();
		manager.init(new MyListener());
		log.info("等待电话呼入...");
	}
	
}
