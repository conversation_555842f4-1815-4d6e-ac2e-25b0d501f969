package com.sungoin.cti.client.demo;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;

public class TestCase15 extends TeleThread {

    private static final Logger LOGGER = LoggerFactory
        .getLogger(TestCase15.class);

    private Call callA = null; //坐席A
    private Call callB = null; //用户B
    private Call callC = null; //坐席C

    public final Object sync = new Object();
    public boolean isWaiting = false;
    public boolean numberReceived = false;

    private Map<String, Call> callMap = new HashMap<String, Call>();

    public TestCase15(TeleThreadEventListener listener) {
        super(listener);
    }

    @Override
    public void run() {
        try {

            //监听关联线程
            ((MyThreadListener) this.getListener()).setMainThread(this);

            LOGGER.debug("呼叫13167062230...");
            this.callA = this.manager.makeCall("31233490", "13167062230",
                false, 30);

            if (this.callA == null) {
                return;
            }
            this.callMap.put("callA", this.callA);
            LOGGER.debug("callA: {}", this.callMap.get("callA"));
            this.callA.setParam("callMap", this.callMap);

            LOGGER.debug("A接通,呼叫15214346165...");
            this.callB = this.manager.makeCall("31233490", "15214346165", true,
                30);
            this.callMap.put("callB", this.callB);
            this.callB.setParam("callMap", this.callMap);
            synchronized (this.sync) {
                while (!this.numberReceived) {
                    this.isWaiting = true;
                    this.sync.wait();
                    LOGGER.debug("主线程被唤醒...");
                }
                if (this.callB.getState() != Constants.CALL_CONTENT) {
                    LOGGER.debug("外呼用户B失败,主线程结束..");
                    return;
                }
            }

            LOGGER.debug("B接通,A和B创建连接");
            this.callA.connectCall(this.callB);
            LOGGER.debug("坐席A与用户B建立通话");

            LOGGER.debug("坐席A同步收码");
            String dtmf = this.callA.receiveDTMFSync(2, "#", 30);
            if ("00".equals(dtmf)) {
                LOGGER.debug("坐席A停止收码");
                this.callA.stopReceiveDTMF();

                LOGGER.debug("收到00,异步外呼15000304023......");
                this.callC = this.manager.makeCall("31233490", "15000304023",
                    true, 30);
                this.callMap.put("callC", this.callC);
                this.callC.setParam("callMap", this.callMap);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        } finally {
            LOGGER.debug("主线程结束。。。。");
            this.finished();
        }

    }

    static class MyThreadListener implements TeleThreadEventListener {

        public TestCase15 mainThread;

        public void setMainThread(TestCase15 mainThread) {
            this.mainThread = mainThread;
        }

        @Override
        public void asyncFinished(BaseEvent event) {
            LOGGER.debug("线程事件...eventType={},callState={},callNo={}", event
                .getType(), event.getCall().getState(), event.getCall()
                .getCallee());

            if (event.getType().equals(EventType.CALLOUT)) {
                if (event.getEventState() != Constants.RET_SUCCESS) {
                    this.mainThread.callA.onHook();
                }
                try {
                    synchronized (this.mainThread.sync) {
                        this.mainThread.isWaiting = false;
                        this.mainThread.numberReceived = true;
                        this.mainThread.sync.notifyAll();
                    }
                } catch (Exception e) {
                    LOGGER.error("error ", e);
                }
                LOGGER.debug("用户B呼叫完成");
            }

        }
    }

    static class MyListener implements CTIEventListener {

        @Override
        public void asyncFinished(BaseEvent event) {
            LOGGER.debug("全局事件...type={},call.state={}" + event.getType(),
                event.getCall().getState());

            if (event.getType().equals(EventType.CALLOUT)) {
                if (event.getEventState() == Constants.RET_SUCCESS) {
                    //获取所有Call集合
                    Map<String, Call> allCallMap = (Map<String, Call>) event
                        .getCall().getParam("callMap");

                    Call callA = allCallMap.get("callA");
                    if (callA.getState() == Constants.CALL_CONTENT) {
                        LOGGER.debug("断开A和B");
                        callA.onHook();
                    }

                    Call callB = allCallMap.get("callB");
                    if (callB.getState() == Constants.CALL_CONTENT) {
                        LOGGER.debug("连接B和C");
                        callB.connectCall(event.getCall());
                    }
                }
            }
        }

        @Override
        public void callIncome(Call call) {
            LOGGER.info("in global listener callIncome...call={}", call);
        }

        @Override
        public void callEnd(Call call) {
            LOGGER.debug("callEnd ...caller={},callee={}", call.getCaller(),
                call.getCallee());
            //获取所有Call集合
            Map<String, Call> callMap = (Map<String, Call>) call
                .getParam("callMap");

            Call callA = callMap.get("callA");
            Call callB = callMap.get("callB");
            Call callC = callMap.get("callC");
            LOGGER.debug("callA {}", callA);
            LOGGER.debug("callB {}", callB);
            LOGGER.debug("callC {}", callC);

            //坐席A挂机
            if (call.equals(callA)) {
                //用户B通话中,坐席C如果未接通需要挂断用户B
                if (callB.getState() == Constants.CALL_CONTENT) {
                    if (callC == null
                        || callC.getState() != Constants.CALL_CONTENT) {
                        callB.onHook();
                    }
                } else {
                    CallManager.getInstance().stopMakeCall(callB.getDeviceId(),
                        callB.getLsh());
                }
            } else if (call.equals(callB)) {
                //坐席A通话中，用户B挂机,需要挂断坐席A
                if (callA.getState() == Constants.CALL_CONTENT) {
                    callA.onHook();
                }
                if (callC != null) {
                    //坐席C已接通
                    if (callC.getState() == Constants.CALL_CONTENT) {
                        callC.onHook();
                    } else {
                        //坐席C在呼叫或者振铃中
                        CallManager.getInstance().stopMakeCall(
                            callC.getDeviceId(), callC.getLsh());
                    }
                }
            } else {
                //用户C挂机
                callB.onHook();
            }
        }
    }

    public static void main(String[] args) throws InterruptedException {
        CallManager manager = CallManager.getInstance();
        manager.init(new MyListener());
        new TestCase15(new MyThreadListener()).start();
    }

}
