/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.api;

import com.sungoin.cti.client.Configuration;
import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.Dispatchers;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.message.BaseRequest;
import com.sungoin.cti.client.message.BaseResponse;
import com.sungoin.cti.client.message.MakeCallRequest;
import com.sungoin.cti.client.message.MessageType;
import com.sungoin.cti.client.socket.MinaClient;
import com.sungoin.cti.client.util.WaitCondition;
import com.sungoin.cti.client.util.WaitUtil;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public final class CallManager implements Serializable {

	private static final long serialVersionUID = 6249181175761882265L;
	private static final Logger log = LoggerFactory.getLogger(CallManager.class);
	private static final CallManager instance = new CallManager();
	private static volatile Map<String, TeleThread> threadMap = new ConcurrentHashMap<String, TeleThread>();
	private static volatile Map<String, Call> callMap = new ConcurrentHashMap<String, Call>();
	private static volatile Map<String, Conference> confMap = new ConcurrentHashMap<String, Conference>();
	private final ScheduledExecutorService ses = Executors.newSingleThreadScheduledExecutor();
	
	private volatile boolean running = false;

	private CallManager() {
	}

	public boolean isRunning() {
		return running;
	}

	public static CallManager getInstance() {
		return instance;
	}

	private final MinaClient socketClient = MinaClient.getInstance();
	private CTIEventListener listener;

	public void setListener(CTIEventListener listener) {
		this.listener = listener;
	}

	public CTIEventListener getListener() {
		return listener;
	}

	/**
	 * 初始化并启动manager 启动包含：启动socekt客户端，启动EDT线程，启动MDT线程 启动时会从classpath根路径下读取cti.properties配置文件,
	 * 如果文件不存在，会读取默认配置cti_default.properties文件。 客户端可以直接修改默认配置文件，或提供自己的cti.properties文件 来改变默认配置项
	 *
	 * @param listener 全局监听实例
	 */
	public void init(CTIEventListener listener) {
		boolean connected = socketClient.initConnector();
		if (!connected) {
			throw new IllegalStateException("连接CTI SERVER 失败！");
		}
		this.listener = listener;
		this.running = true;
		Dispatchers.getEventDispatcher().start();
		Dispatchers.getMessageDispatcher().start();
		//启动定时任务
		startSchedule();
	}
	
	private void startSchedule() {
		long oneDay = 24 * 60 * 60 * 1000;
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 3);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		long initDelay = calendar.getTimeInMillis() - System.currentTimeMillis();
		if (initDelay < 0) {
			initDelay = oneDay + initDelay;
		}
		ses.scheduleAtFixedRate(new Runnable() {
			@Override
			public void run() {
				checkDeadThread();
			}
		}, initDelay, oneDay, TimeUnit.MILLISECONDS);
	}

	public TeleThread addTeleThread(String name, TeleThread thread) {
		return threadMap.put(name, thread);
	}

	public TeleThread getTeleThread(String name) {
		return threadMap.get(name);
	}

	public TeleThread removeTeleThread(String name) {
		return threadMap.remove(name);
	}

	/**
	 * 外呼，当为同步模式时，外呼时长超过超时时长后会停止外呼， 在超时前不能通过程序停止外呼； 当为异步模式时，可在呼叫发起后通过stopMakeCall停止呼叫。
	 *
	 * @param caller	主叫
	 * @param callee	被叫
	 * @param async	异步
	 * @param timeoutSeconds	超时时长（秒），仅在同步模式时有效
	 * @return
	 */
	public Call makeCall(String caller, String callee, boolean async, long timeoutSeconds) {
		return this.makeCall(caller, callee, null, async, timeoutSeconds, null);
	}
	
	public Call makeCall(String caller, String callee, String origCallee, boolean async, long timeoutSeconds, String threadName) {
		log.debug("makeCall method invoked...caller={},callee={},async={},timeout={}", caller, callee, async, timeoutSeconds);
		Call call = null;
		MakeCallRequest req = new MakeCallRequest(caller, callee, origCallee);
		if(threadName != null) {
			req.setThreadName(threadName);
		}
		BaseResponse response = (BaseResponse) req.submit(Configuration.getSocektTimeout());
		if (response != null && response.getEventState() == Constants.RET_SUCCESS) {
			if (!async) {
				BaseEvent event = null;
				try {
					event = (BaseEvent) WaitUtil.await(response.getThreadName(), WaitCondition.EVENT,
						EventType.CALLOUT, response.getLsh(), timeoutSeconds * 1000);
				} catch (Exception ex) {
					log.error(ex.getMessage(), ex);
					this.stopMakeCall(response.getDeviceID(), response.getLsh());
				}
				if (event != null && event.getEventState() == 1) {
					call = new Call(event.getDeviceID(), event.getLsh(), caller, callee);
					call.setState(Constants.CALL_CONTENT);
				}
			} else {
				call = new Call(response.getDeviceID(), response.getLsh(), caller, callee);
				call.setState(Constants.CALL_ING);
			}
		}
		if (call != null) {
			CallManager.getInstance().putCall(call);
		}
		return call;
	}

	/**
	 * 停止异步外呼
	 *
	 * @param callDeviceId 呼叫设备ID
	 * @param lsh	呼叫设备通话流水号
	 */
	public int stopMakeCall(String callDeviceId, int lsh) {
		log.debug("stopMakeCall method invoked...callId={},lsh={}", callDeviceId, lsh);
		Call call = this.findCallByCallID(callDeviceId, lsh);
		if (call != null) {
			if (call.getState() == Constants.CALL_ING || call.getState() == Constants.CALL_ALERT) {
				BaseRequest req = new BaseRequest(callDeviceId, MessageType.ONHOOK, lsh, call.getThreadName());
				req.send();
				return Constants.RET_SUCCESS;
			} else {
				log.warn("stopMakeCall fail,call state error,expected state is 1: but current state is : {}", call.getState());
				return Constants.RET_CALL_STATE_FAIL;
			}
		}
		return Constants.RET_FAIL;
	}

	/**
	 * 获取当前所有呼叫对象集合
	 *
	 * @return
	 */
	public List<Call> getCallList() {
		log.debug("getCallList method invoked...");
		return new ArrayList<Call>(callMap.values());
	}

	/**
	 * 获取当前所有会议对象集合
	 *
	 * @return
	 */
	public List<Conference> getConfList() {
		log.debug("getConfList method invoked...");
		return new ArrayList<Conference>(confMap.values());
	}

	/**
	 * 根据参数获取呼叫对象
	 *
	 * @param paramName	参数名
	 * @param paramValue	参数值
	 * @return
	 */
	public List<Call> findCallByParam(String paramName, String paramValue) {
		log.debug("findCallByParam method invoked...paranName={},paramValue={}", paramName, paramValue);
		List<Call> list = new ArrayList<Call>();
		for (Call call : getCallList()) {
			if (call.getParam(paramName) != null && call.getParam(paramName).equals(paramValue)) {
				list.add(call);
			}
		}
		return list;
	}

	/**
	 * 根据设备ID和设备流水号获得通话对象
	 *
	 * @param deviceId	设备ID
	 * @param lsh	设备通话流水号
	 * @return	通话对象
	 */
	public Call findCallByCallID(String deviceId, int lsh) {
		log.debug("findCallByCallID method invoked...deviceId={},lsh={}", deviceId, lsh);
		if (StringUtils.isEmpty(deviceId)) {
			return null;
		}
		String key = deviceId + lsh;
		return callMap.get(key);
	}

	public void putCall(Call call) {
		log.debug("putCall method invoked...call={} ", call);
		String key = call.getDeviceId() + call.getLsh();
		callMap.put(key, call);
	}

	public void removeCall(Call call) {
		log.debug("removeCall method invoked...call={} size is {}", call, callMap.size() - 1);
		String key = call.getDeviceId() + call.getLsh();
		callMap.remove(key);
	}

	public void removeCall(String deviceId, int lsh) {
		log.debug("removeCall method invoked...deviceId={},lsh={}", deviceId, lsh);
		String key = deviceId + lsh;
		callMap.remove(key);
	}

	public Conference findConfByParam(String paramName, String paramValue) {
		return null;
	}

	/**
	 * 根据会议ID获取会议对象
	 *
	 * @param confDeviceId	会议ID
	 * @return	会议对象
	 */
	public Conference findConfByConfID(String confDeviceId) {
		log.debug("findConfByConfID method invoked...confDeviceId={}", confDeviceId);
		return confMap.get(confDeviceId);
	}

	/**
	 * 添加会议成员
	 *
	 * @param confDeviceId	会议ID
	 * @param call	通话对象
	 * @param mode	会议模式
	 * @return	会议对象
	 */
	public Conference addMember(String confDeviceId, Call call, CONF_MODE mode) {
		log.debug("addMember method invoked...confDeviceId={},call={},mode={}", confDeviceId, call, mode);
		Conference conf = confMap.get(confDeviceId);
		if (conf == null) {
			log.debug(call.getDeviceId() + "是第一位参会者！");
			conf = new Conference(confDeviceId);
		}
		conf.addMember(call, mode);
		confMap.put(confDeviceId, conf);
		return conf;
	}

	public void removeConf(String confDeviceId) {
		confMap.remove(confDeviceId);
		log.debug("removeConf method invoked...confDeviceId={} cnfMap size is {}", confDeviceId, confMap.size());
	}

	/**
	 * 停止manager服务
	 */
	public void shutdown() {
		log.debug("shutdown method invoked...");
		this.running = false;
		MinaClient.getInstance().close();
		this.ses.shutdownNow();
	}
	
	private void checkDeadThread() {
		long now = System.currentTimeMillis();
		long checkTime = 2 * 60 * 60 * 1000;
		for(TeleThread t : threadMap.values()) {
			if(now - t.getStartTime() > checkTime ) {
				t.interrupt();
			}
		}
	}
}
