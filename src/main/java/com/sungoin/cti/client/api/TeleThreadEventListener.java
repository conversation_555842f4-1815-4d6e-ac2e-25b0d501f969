/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.api;

import com.sungoin.cti.client.event.BaseEvent;

/**
 *
 * <AUTHOR> 2015-5-14 线程事件监听
 * 在监听事件中可以执行耗时的方法，不影响EDT线程.
 */
public interface TeleThreadEventListener {

	/**
	 * 线程执行中的异步事件返回
	 * @param event 会议事件返回ConfEvent，收码事件返回ReceiveDTMFEvent，
	 * 其他所有事件都返回BaseEvent，可以根据event.getType()取得事件类型后进行类型强转。
	 * 事件类型参见com.sungoin.cti.client.event.EventType。
	 */
	public void asyncFinished(BaseEvent event);
}
