/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.api;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;

/**
 *
 * <AUTHOR> 2015-5-14
 * 通话基础线程，所有来电或去电都需要启动一个新的线程来处理业务，
 * 新的线程必须继承此基础线程
 */
public abstract class TeleThread extends Thread implements Serializable {
	private static final long serialVersionUID = 7090797066558117100L;
	
	/**
	 * 线程事件的监听器
	 */
	protected TeleThreadEventListener listener;
	
	protected transient CallManager manager = CallManager.getInstance();
	
	private long startTime;

	public long getStartTime() {
		return startTime;
	}
	
	private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
		in.defaultReadObject();
		manager = CallManager.getInstance();
	}

	public TeleThread() {
		this.setName("CTI-" + Integer.toHexString(CallManager.getInstance().hashCode()) + "-" + this.getName());
	}

	public TeleThread(TeleThreadEventListener listener) {
		this.listener = listener;
		this.setName("CTI-" + Integer.toHexString(CallManager.getInstance().hashCode()) + "-" + this.getName());
	}
	
	/**
	 * 获取线程时间的监听器
	 * @return 
	 */
	public TeleThreadEventListener getListener() {
		return listener;
	}

	public void setListener(TeleThreadEventListener listener) {
		this.listener = listener;
	}

	/**
	 * 获取CallManager对象
	 * @return CallManager
	 */
	public CallManager getManager() {
		return manager;
	}
	
	/**
	 * 清理方法，子线程在业务逻辑处理结束后需要调用次方法清理资源
	 * 一般的调用逻辑为：在子类的run方法中:
	 * try{
	 *		XXX...
	 * }catch(Exception ex){
	 *		XXX...
	 * } finally {
	 *		this.finished();//importent!
	 * }
	 */
	protected final void finished() {
		manager.removeTeleThread(this.getName());
	}

	@Override
	public final synchronized void start() {
		this.startTime = System.currentTimeMillis();
		manager.addTeleThread(this.getName(), this);
		super.start(); 
	}
	
	/**
	 * 等待
	 * @param timeMills 毫秒
	 */
	protected void await(long timeMills) {
		try {
			Thread.sleep(timeMills);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}
