/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.api;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sungoin.cti.client.Configuration;
import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.KeyUp;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import com.sungoin.cti.client.message.BaseRequest;
import com.sungoin.cti.client.message.BaseResponse;
import com.sungoin.cti.client.message.BroadCastRequest;
import com.sungoin.cti.client.message.ConferenceRequest;
import com.sungoin.cti.client.message.ConnectRequest;
import com.sungoin.cti.client.message.DetectSpeechRequest;
import com.sungoin.cti.client.message.MessageType;
import com.sungoin.cti.client.message.PlayDTMFRequest;
import com.sungoin.cti.client.message.PlayNumRequest;
import com.sungoin.cti.client.message.PlayRequest;
import com.sungoin.cti.client.message.ReceiveDTMFRequest;
import com.sungoin.cti.client.message.RecordRequest;
import com.sungoin.cti.client.util.WaitCondition;
import com.sungoin.cti.client.util.WaitUtil;
import java.io.Serializable;

/**
 * <AUTHOR> 2015-5-13
 */
public class Call implements Serializable {

	private static final long serialVersionUID = 1999669550340307889L;
	private static final int socketTimeOut = Configuration.getSocektTimeout();
	private static final Logger log = LoggerFactory.getLogger(Call.class);
	private String deviceId;
	private int lsh;
	private String caller;
	private String callee;
	private volatile Map<String, Object> paramMap = new ConcurrentHashMap<String, Object>();
	private Conference currentConf;
	private volatile int state;
	private volatile boolean isPlaying;
	private String callThreadName;
    private final long timeStamp;
    private volatile String traceId;

	public boolean isIsPlaying() {
		return this.isPlaying;
	}

	public void setIsPlaying(boolean isPlaying) {
		this.isPlaying = isPlaying;
	}

	public void attachTeleThread(TeleThread callThread) {
		this.callThreadName = callThread.getName();
	}

	public String getThreadName() {
		return this.callThreadName == null ? Thread.currentThread().getName()
			: this.callThreadName;
	}

	public int getState() {
		return this.state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public Call() {
        this.timeStamp = System.currentTimeMillis();
        this.setTraceId(this.caller + "-" + this.deviceId + "-" + this.timeStamp);
	}

	public Call(String deviceId, int lsh) {
		this.deviceId = deviceId;
		this.lsh = lsh;
        this.timeStamp = System.currentTimeMillis();
        this.setTraceId(this.caller + "-" + this.deviceId + "-" + this.timeStamp);
	}

	public Call(String deviceId, int lsh, String caller, String callee) {
		this.deviceId = deviceId;
		this.lsh = lsh;
		this.caller = caller;
		this.callee = callee;
        this.timeStamp = System.currentTimeMillis();
        this.setTraceId(this.caller + "-" + this.deviceId + "-" + this.timeStamp);
	}

	/**
	 * 获取主叫
	 *
	 * @return
	 */
	public String getCaller() {
		return this.caller;
	}

	public void setCaller(String caller) {
		this.caller = caller;
	}

	/**
	 * 获取被叫
	 *
	 * @return
	 */
	public String getCallee() {
		return this.callee;
	}

	public void setCallee(String callee) {
		this.callee = callee;
	}

	/**
	 * 获取通话设备ID
	 *
	 * @return
	 */
	public String getDeviceId() {
		return this.deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	/**
	 * 获取通话设备流水号
	 *
	 * @return
	 */
	public int getLsh() {
		return this.lsh;
	}

	public void setLsh(int lsh) {
		this.lsh = lsh;
	}

	/**
	 * 获取当前通话所在的会议
	 *
	 * @return 当前通话所在的会议，如无会议，返回null
	 */
	public Conference getCurrentConf() {
		return this.currentConf;
	}

	public void setCurrentConf(Conference currentConf) {
		this.currentConf = currentConf;
	}

	/**
	 * 振铃
	 *
	 * @return
	 */
	public int alert() {
		if (this.state != Constants.CALL_ING) {
			log.warn(
				"alert fail,call state error, expected state is 1: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		BaseRequest req = new BaseRequest(this.deviceId, MessageType.ALERT,
			this.lsh, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 异步应答 调用方需等待200毫秒以上，等待应答事件返回后方可操作
	 *
	 * @return
	 */
	public int answer() {
		if (this.state != Constants.CALL_ALERT
			&& this.state != Constants.CALL_ING) {
			log.warn(
				"answer fail,call state error, expected state is 1: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		BaseRequest req = new BaseRequest(this.deviceId, MessageType.ANSWER,
			this.lsh, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 同步应答
	 *
	 * @param millisecond 毫秒
	 * @return
	 */
	public int answerSync(int millisecond) {
		if (this.state != Constants.CALL_ALERT
			&& this.state != Constants.CALL_ING) {
			log.warn(
				"answerSync fail,call state error, expected state is 1: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		BaseRequest req = new BaseRequest(this.deviceId, MessageType.ANSWER,
			this.lsh, this.getThreadName());
		BaseResponse response = req.submit(socketTimeOut);
		if (response != null
			&& response.getEventState() == Constants.RET_SUCCESS) {
			try {
				WaitUtil.await(response.getThreadName(), WaitCondition.EVENT,
					EventType.OFFHOOK, response.getLsh(), millisecond);
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
			return response.getEventState();
		}
		return response == null ? Constants.RET_TIMEOUT : response
			.getEventState();
	}

	/**
	 * 同异步放音
	 *
	 * @param file 传入文件绝对路径
	 * @param loop 是否循环放音
	 * @param isQueue 是否进入队列
	 * @param async 是否异步放音
	 * @param timeoutSeconds 超时时间
	 * @param maxSecond 放音时间
	 * @return
	 */
	public int play(String file, boolean loop, boolean isQueue, boolean async,
		long timeoutSeconds, int maxSecond) {
		if (this.state != Constants.CALL_ALERT
			&& this.state != Constants.CALL_CONTENT) {
			log.warn(
				"play fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		if (this.isPlaying && !isQueue) {
			log.warn("当前正在播放语音，且新语音非队列播放，需要先同步结束当前语音!");
			int ret = this.stopPlaySync(socketTimeOut);
			log.info("同步停止上一个语音返回：{}", ret);
		}
		this.isPlaying = true;
		PlayRequest req = new PlayRequest(this.deviceId, this.lsh, file,
			String.valueOf(loop), String.valueOf(isQueue), maxSecond,
			this.getThreadName());
		if (async) {
			req.send();
			return Constants.RET_SUCCESS;
		} else {
			BaseResponse response = req.submit(socketTimeOut);
			if (response != null
				&& response.getEventState() == Constants.RET_SUCCESS) {
				BaseEvent event = null;
				try {
					event = (BaseEvent) WaitUtil.await(
						response.getThreadName(), WaitCondition.EVENT,
						EventType.PLAYEND, response.getLsh(),
						timeoutSeconds * 1000);
				} catch (Exception ex) {
					log.error(ex.getMessage(), ex);
					this.stopPlay();
				}
				if (event != null) {
					return event.getEventState();
				} else {
					return Constants.RET_TIMEOUT;
				}
			} else {
				return response == null ? Constants.RET_TIMEOUT : response
					.getEventState();
			}
		}
	}
    
    /**
     * 广播：异步执行，无结束事件
     * @param file
     * @return 
     */
    public int broadCast(String file) {
        BroadCastRequest req = new BroadCastRequest(this.deviceId, this.lsh, this.getThreadName(), file);
        req.send();
		return Constants.RET_SUCCESS;
    }
    
    /**
     * 语音识别
     * @param file
     * @param timeout
     * @return 
     */
    public int detectSpeech(String file, long timeout) {
        DetectSpeechRequest req = new DetectSpeechRequest(this.deviceId, this.lsh, this.getThreadName(), file, timeout);
        req.send();
		return Constants.RET_SUCCESS;
    }

    /**
     * 停止语音识别
     * @return 
     */
    public int stopDetectSpeech() {
        BaseRequest req = new BaseRequest(this.deviceId, MessageType.STOPDETECTSPEECH, this.lsh, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
    }
	/**
	 * 播放数字
	 *
	 * @param headFile 播放前置音频，可以为空
	 * @param number 要播放的数字
	 * @param endFile 播放后置音频，可以为空
	 * @param async 是否异步播放
	 * @param timeoutSeconds 超时时长（秒）
	 * @return
	 */
	public int playNum(String headFile, String number, String endFile,
		boolean async, long timeoutSeconds) {
		if (this.state != Constants.CALL_ALERT
			&& this.state != Constants.CALL_CONTENT) {
			log.warn(
				"playNum fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		if (StringUtils.isNotBlank(number)) {
			if (headFile != null) {
				this.play(headFile, false, true, true, 0, 0);
				this.await(100);
			}
			PlayNumRequest req = new PlayNumRequest(this.deviceId, this.lsh,
				number, this.getThreadName());
			req.send();
			if (endFile != null) {
				this.await(100);
				this.play(endFile, false, true, async, timeoutSeconds, 0);
			}
		}
		return Constants.RET_SUCCESS;
	}

	/**
	 * 停止放音
	 *
	 * @return
	 */
	public int stopPlay() {
		if (this.state != Constants.CALL_ALERT
			&& this.state != Constants.CALL_CONTENT) {
			log.warn(
				"stopPlay fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		BaseRequest req = new BaseRequest(this.deviceId, MessageType.STOPPLAY,
			this.lsh, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 同步停止放音
	 *
	 * @param timeoutMillis
	 * @return
	 */
	public int stopPlaySync(int timeoutMillis) {
		if (this.state != Constants.CALL_ALERT
			&& this.state != Constants.CALL_CONTENT) {
			log.warn(
				"stopPlaySync fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		BaseRequest req = new BaseRequest(this.deviceId, MessageType.STOPPLAY,
			this.lsh, this.getThreadName());
		BaseResponse response = req.submit(socketTimeOut);
		if (response != null
			&& response.getEventState() == Constants.RET_SUCCESS) {
			BaseEvent event = null;
			try {
				event = (BaseEvent) WaitUtil.await(response.getThreadName(),
					WaitCondition.EVENT, EventType.PLAYEND, response.getLsh(),
					timeoutMillis);
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
			return event == null ? Constants.RET_TIMEOUT : response
				.getEventState();
		} else {
			return response == null ? Constants.RET_TIMEOUT : response
				.getEventState();
		}
	}

	/**
	 * 连接通话
	 *
	 * @param call
	 * @return
	 */
	public int connectCall(Call call, boolean sameDsp) {
		if (this.state != Constants.CALL_CONTENT || call.getState() != Constants.CALL_CONTENT) {
			log.warn("connectCall fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		ConnectRequest req = new ConnectRequest(call.getLsh(), this.deviceId,
			this.lsh, call.getDeviceId(), this.getThreadName(), sameDsp);
		req.send();
		return Constants.RET_SUCCESS;
	}
    
    public int connectCall(Call call) {
        return connectCall(call, true);
    }

	/**
	 * 断开连接
	 *
	 * @param call
	 * @return
	 */
	public int disconnectCall(Call call) {
		if (this.state != Constants.CALL_CONTENT || call.getState() != Constants.CALL_CONTENT) {
			log.warn(
				"disconnectCall fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		ConnectRequest req = new ConnectRequest(call.getLsh(), this.deviceId,
			this.lsh, call.getDeviceId(), this.getThreadName());
		req.setOperateType(MessageType.DISCONNECT);
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 异步录音
	 *
	 * @param filename 录音绝对路径
	 * @param maxTimeSeconds 最大录音时间
	 * @param isAppend 是否在原文件追加
	 * @return
	 */
	public int record(String filename, int maxTimeSeconds, boolean isAppend) {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"record fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		RecordRequest req = new RecordRequest(this.deviceId, this.lsh,
			filename, maxTimeSeconds, isAppend, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 同步录音
	 *
	 * @param filename 录音绝对路径
	 * @param maxTimeSeconds 最大录音时间
	 * @param isAppend 是否在原文件追加
	 * @return
	 */
	public int recordSync(String filename, int maxTimeSeconds, boolean isAppend) {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"record fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		RecordRequest req = new RecordRequest(this.deviceId, this.lsh,
			filename, maxTimeSeconds, isAppend, this.getThreadName());
		BaseResponse response = req.submit(socketTimeOut);
		if (response != null && response.getEventState() == Constants.RET_SUCCESS) {
			try {
				WaitUtil.await(response.getThreadName(), WaitCondition.EVENT,
					EventType.RECORDEND, response.getLsh(), maxTimeSeconds * 1000 + socketTimeOut);
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
			return response.getEventState();
		}
		return response == null ? Constants.RET_TIMEOUT : response.getEventState();
	}

	/**
	 * 停止录音
	 *
	 * @return
	 */
	public int stopRecord() {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"stopRecord fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		BaseRequest req = new BaseRequest(this.deviceId,
			MessageType.STOPRECORD, this.lsh, this.getThreadName());
		BaseResponse response = req.submit(socketTimeOut);
		if (response != null) {
			return response.getEventState();
		}
		return Constants.RET_TIMEOUT;
	}

	/**
	 * 挂机
	 *
	 * @return
	 */
	public int onHook() {
//        if (this.state != Constants.CALL_CONTENT) {
//            log.warn(
//                "onHook fail,call state error, expected state is 3: but current state is : {}",
//                this.state);
//            return Constants.RET_CALL_STATE_FAIL;
//        }
		BaseRequest req = new BaseRequest(this.deviceId, MessageType.ONHOOK,
			this.lsh, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 同步收码
	 *
	 * @param maxdigits 按鍵長度最小為1
	 * @param terminatedchar 結束字符串
	 * @param timeoutSeconds 同步超時時間
	 * @return
	 */
	public String receiveDTMFSync(int maxdigits, String terminatedchar,
		int timeoutSeconds) {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"receiveDTMFSync fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return "";
		}
		if (maxdigits < 1) {
			throw new IllegalStateException(" maxdigits is not lt 1 !");
		}
		timeoutSeconds = timeoutSeconds * 1000;
		KeyUp.initDtfm(this.deviceId, terminatedchar, maxdigits);
		ReceiveDTMFRequest req = new ReceiveDTMFRequest(this.deviceId,
			this.lsh, maxdigits, terminatedchar, this.getThreadName());
		BaseResponse response = req.submit(socketTimeOut);
		if (response != null
			&& response.getEventState() == Constants.RET_SUCCESS) {
			ReceiveDTMFEvent event = null;
			try {
				event = (ReceiveDTMFEvent) WaitUtil.await(
					response.getThreadName(), WaitCondition.EVENT,
					EventType.RECEIVEDTMF, response.getLsh(), timeoutSeconds);
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
			this.stopReceiveDTMF();
			if (event != null && event.getEventState() == 1) {
				return event.getDtmf();
			}
		}
		return "";
	}

	/**
	 * 异步收码，需在调用完成后关闭收码
	 *
	 * @return
	 */
	public int receiveDTMFAsync() {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"receiveDTMFAsync fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		ReceiveDTMFRequest req = new ReceiveDTMFRequest(this.deviceId,
			this.lsh, 1, null, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 停止收码
	 *
	 * @return
	 */
	public int stopReceiveDTMF() {
		KeyUp.cleanDtmf(this.deviceId);
		BaseRequest req = new BaseRequest(this.deviceId,
			MessageType.STOPRECEIVEDTMF, this.lsh, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 发码 手动发送DTMF码给中间件
	 *
	 * @param dtfm
	 * @return
	 */
	public int playDTMF(String dtfm) {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"playDTMF fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		PlayDTMFRequest req = new PlayDTMFRequest(dtfm, this.deviceId,
			MessageType.PLAYDTMF, this.lsh, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 获取参数值
	 *
	 * @param paramName
	 * @return
	 */
	public Object getParam(String paramName) {
		return this.paramMap.get(paramName);
	}

	/**
	 * 设置参数
	 *
	 * @param paramName
	 * @param paramValue
	 */
	public void setParam(String paramName, Object paramValue) {
		if (paramValue == null) {
			this.paramMap.remove(paramName);
		} else {
			this.paramMap.put(paramName, paramValue);
		}

	}

	/**
	 * 获取所有参数
	 *
	 * @return
	 */
	public Map<String, Object> getAllParam() {
		return this.paramMap;
	}

	/**
	 * 等待=Thread.sleep()
	 *
	 * @param timeMillis 毫秒
	 */
	private void await(long timeMillis) {
		try {
			Thread.sleep(timeMillis);
		} catch (Exception ex) {
			log.error(ex.getMessage());
		}
	}

	/**
	 * 创建会议
	 *
	 * @param mode 模式为 听和说、只听、只说
	 * @return
	 */
	public int createConf(CONF_MODE mode) {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"createConf fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		log.debug(this.deviceId + " 创建会议。。。。。。");
		ConferenceRequest req = new ConferenceRequest(this.deviceId, this.lsh,
			mode, this.getThreadName());
		req.send();
		return Constants.RET_SUCCESS;
	}

	/**
	 * 同步创建会议
	 *
	 * @param mode
	 * @param timeMillis
	 * @return
	 */
	public int createConfSync(CONF_MODE mode, int timeMillis) {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"createConfSync fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		log.debug(this.deviceId + " 创建会议。。。。。。");
		ConferenceRequest req = new ConferenceRequest(this.deviceId, this.lsh,
			mode, this.getThreadName());
		BaseResponse response = req.submit(socketTimeOut);
		if (response != null
			&& response.getEventState() == Constants.RET_SUCCESS) {
			BaseEvent event = null;
			try {
				event = (BaseEvent) WaitUtil.await(response.getThreadName(),
					WaitCondition.EVENT, EventType.JOINCONF, response.getLsh(),
					timeMillis);
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
			return event == null ? Constants.RET_TIMEOUT : event
				.getEventState();
		}
		return response == null ? Constants.RET_TIMEOUT : response
			.getEventState();
	}

	/**
	 * 加入会议
	 *
	 * @param conf 已存在的会议对象
	 * @param mode 模式为 听和说、只听、只说
	 * @return
	 */
	public int joinConf(Conference conf, CONF_MODE mode) {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"joinConf fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		log.debug(this.deviceId + " 加入会议。。。。。。");
		if (this.currentConf != null) {
			log.warn("already in a conf");
			return Constants.RET_FAIL;
		} else {
			ConferenceRequest req = new ConferenceRequest(this.deviceId,
				this.lsh, mode, this.getThreadName());
			req.setOperateType(MessageType.JOINCONF);
			req.setConfID(conf.getConfDeviceID());
			req.send();
			return Constants.RET_SUCCESS;
		}
	}

	/**
	 * 离开会议
	 *
	 * @return
	 */
	public int leaveConf() {
		if (this.state != Constants.CALL_CONTENT) {
			log.warn(
				"leaveConf fail,call state error, expected state is 3: but current state is : {}",
				this.state);
			return Constants.RET_CALL_STATE_FAIL;
		}
		if (this.currentConf == null) {
			log.warn("not need leave a CONF.");
			return Constants.RET_FAIL;
		} else {
			ConferenceRequest req = new ConferenceRequest(this.deviceId,
				this.lsh, CONF_MODE.ADD, this.getThreadName());
			req.setOperateType(MessageType.LEAVECONF);
			req.send();
			return Constants.RET_SUCCESS;
		}
	}

	@Override
	public int hashCode() {
		int hash = 7;
		hash = 67 * hash
			+ (this.deviceId != null ? this.deviceId.hashCode() : 0);
		hash = 67 * hash + this.lsh;
		return hash;
	}

	@Override
	public boolean equals(Object obj) {
		if (obj == null) {
			return false;
		}
		if (this.getClass() != obj.getClass()) {
			return false;
		}
		final Call other = (Call) obj;
		if ((this.deviceId == null) ? (other.deviceId != null) : !this.deviceId
			.equals(other.deviceId)) {
			return false;
		}
		if (this.lsh != other.lsh) {
			return false;
		}
		return true;
	}

	@Override
	public String toString() {
		return "Call{" + "deviceId=" + this.deviceId + ", lsh=" + this.lsh
			+ ", caller=" + this.caller + ", callee=" + this.callee
			+ ", state=" + this.state + ", currentConf=" + this.currentConf
			+ '}';
	}

    public String getTraceId() {
        return this.traceId;
    }
    
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
}
