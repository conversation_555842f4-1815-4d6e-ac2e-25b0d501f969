/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.client.api;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 * 2015-5-16
 */
public class Conference implements Serializable {
	private static final long serialVersionUID = 1673864890671549008L;
	
	private final String confDeviceID;
	private final Date createTime = new Date();
	private volatile Map<Call,CONF_MODE> members = new HashMap<Call,CONF_MODE>();

	public Conference(String confDeviceID) {
		this.confDeviceID = confDeviceID;
	}

    public String getConfDeviceID() {
        return confDeviceID;
    }

	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 获取所有参会成员
	 * @return 
	 */
	public Map<Call, CONF_MODE> getMembers() {
		return members;
	}
	
	/**
	 * 添加会议成员
	 * @param call	通话对象
	 * @param mode	会议模式
	 */
	public void addMember(Call call, CONF_MODE mode) {
		this.members.put(call, mode);
		call.setCurrentConf(this);
	}
	
	/**
	 * 移除会议成员
	 * @param call	通话对象
	 */
	public void removeMember(Call call) {
		this.members.remove(call);
		call.setCurrentConf(null);
	}
}
