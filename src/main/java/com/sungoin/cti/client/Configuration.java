/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client;

import com.sungoin.cti.client.exception.CTILoadPropertyFailException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 
 * 2015-5-20
 * 系统优先读取根路径下的cti.properties文件，此文件一般由业务系统提供，可自定义参数配置
 * 如果cti.properties文件不存在，系统会读取默认配置文件cti_default.properties
 */
public final class Configuration {

	private static final Logger log = LoggerFactory.getLogger(Configuration.class);
	private static final Properties prop = new Properties();

	static {
		InputStream in = Configuration.class.getClassLoader().getResourceAsStream(Constants.CUSTOM_PROP_NAME);
		if(in == null) {
			log.warn("找不到cti配置文件，使用默认配置文件：{}",Constants.DEFAULT_PROP_NAME);
			in = Configuration.class.getClassLoader().getResourceAsStream(Constants.DEFAULT_PROP_NAME);
		}
		try {
			prop.load(in);
		} catch (IOException ex) {
			log.error(ex.getMessage(),ex);
			throw new CTILoadPropertyFailException("读取配置文件失败！" + ex.getMessage());
		}
	}

	private Configuration() {
	}
	
	public static String getServerIp() {
		return prop.getProperty(Constants.SERVER_IP);
	}
	
	public static int getServerPort() {
		return Integer.parseInt(prop.getProperty(Constants.SERVER_PORT));
	}
	
	public static int getReconnectTime() {
		return Integer.parseInt(prop.getProperty(Constants.RECONNECT_TIME));
	}
	
	public static int getMessageQueueSize() {
		return Integer.parseInt(prop.getProperty(Constants.MESSAGE_QUEUE_SIZE));
	}
	
	public static int getEventQueueSize() {
		return Integer.parseInt(prop.getProperty(Constants.EVENT_QUEUE_SIZE));
	}
	
	public static int getSocektTimeout() {
		return Integer.parseInt(prop.getProperty(Constants.SOCKET_TIMEOUT_TIMEMILLIS));
	}
	
	public static int getDestroyTimeout() {
		return Integer.parseInt(prop.getProperty(Constants.CALLEND_DESTROY_DELAY_TIMEMILLIS));
	}

}
