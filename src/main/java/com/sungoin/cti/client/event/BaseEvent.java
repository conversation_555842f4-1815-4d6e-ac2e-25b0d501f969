/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.message.BaseResponse;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class BaseEvent extends BaseResponse {
    
    public BaseEvent(String eventType, int lsh, int eventState, String threadName, String deviceID, int errorCode) {
		super(lsh, eventState, threadName, deviceID, eventType, errorCode);
	}
	
	public Call getCall() {
		return CallManager.getInstance().findCallByCallID(deviceID, lsh);
	}
}
