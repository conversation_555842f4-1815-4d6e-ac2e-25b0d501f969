/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.client.event;

import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.Configuration;
import com.sungoin.cti.client.api.Call;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 
 * <AUTHOR>
 * 2015-5-18
 */
public class DistroyCallTask implements Runnable {
	private static final Logger log = LoggerFactory.getLogger(DistroyCallTask.class);
	
	private final Call call;

	public DistroyCallTask(Call call) {
		this.call = call;
	}
	
	@Override
	public void run() {
		try {
			Thread.sleep(Configuration.getDestroyTimeout());
			CallManager.getInstance().removeCall(call);
		} catch (Exception ex) {
			log.error(ex.getMessage(),ex);
		}
	}

}
