/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

import com.sungoin.cti.client.api.CONF_MODE;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.Configuration;
import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.Dispatcher;
import com.sungoin.cti.client.KeyUp;
import com.sungoin.cti.client.message.BaseResponse;
import com.sungoin.cti.client.util.WaitCondition;
import com.sungoin.cti.client.util.WaitUtil;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-13 EDT 线程
 */
public class EventDispatcher extends Thread implements Dispatcher {

	private static final Logger log = LoggerFactory.getLogger(EventDispatcher.class);
	private final static EventDispatcher instance = new EventDispatcher();

	private final ExecutorService es = Executors.newCachedThreadPool();
	private final BlockingQueue<BaseEvent> queue = new ArrayBlockingQueue<BaseEvent>(Configuration.getEventQueueSize());
	private final CallManager manager = CallManager.getInstance();

	public static EventDispatcher getInstance() {
		return instance;
	}

	private EventDispatcher() {
		super("CTI Event Dispatch Thread");
	}

	@Override
	public void run() {
		log.info("EDT Thread started...");
		while (manager.isRunning()) {
			doDispatch();
		}
		es.shutdownNow();
	}

	private void doDispatch() {
		try {
			BaseEvent event = queue.take();
			log.debug("轮训到要处理的事件：{}", event);
			String eventType = event.getType();
			//来电事件和挂机事件特殊处理
			if (eventType.equals(EventType.CALL_INCOME)) {
				String caller = ((CallIncomeEvent) event).getCaller();
				String callee = ((CallIncomeEvent) event).getCallee();
				Call call = new Call(event.getDeviceID(), event.getLsh(), caller, callee);
				es.submit(new GlobalTask(call, GlobalTask.CALL_INCOME));
			} else if (eventType.equals(EventType.ONHOOK)) {
				Call call = event.getCall();
				if (call != null) {
					es.submit(new GlobalTask(call, GlobalTask.CALL_END));
					es.submit(new DistroyCallTask(call));
				} else {
					log.warn("根据设备ID：{},流水号{}，找不到对应的call对象！",event.getDeviceID(), event.getLsh());
				}
			} else if (event.getThreadName() != null
				&& WaitUtil.isWaiting(event.getThreadName(), WaitCondition.EVENT, event.getType(), event.getLsh())) {
				//正在同步块中等待事件，不触发异步事件监听,直接唤醒线程
				setCallAndConfState(event);
				WaitUtil.notifyWaiter(event.getThreadName(), WaitCondition.EVENT, event.getType(), event.getLsh(), event);
			} else {
				setCallAndConfState(event);
				//其他事件触发asyncFinished方法
				es.submit(new EventExecuteTask(event));
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
	}

	@Override
	public void dispatch(BaseResponse event) {
		boolean notFull = true;
		if (EventType.RECEIVEDTMF.equals(event.getType())
			&& WaitUtil.isWaiting(event.getThreadName(), WaitCondition.EVENT, EventType.RECEIVEDTMF, event.getLsh())) {
			ReceiveDTMFEvent ev = (ReceiveDTMFEvent) event;
			String code = KeyUp.appendDTMF(ev.getDeviceID(), ev.getDtmf());
			if (code != null) {
				ev.setDtmf(code);
				notFull = queue.offer(ev);
			}
		} else {
			notFull = queue.offer((BaseEvent) event);
		}
		if (!notFull) {
			log.warn("事件队列满！当前队列数：{},请调大队列数量！", Configuration.getEventQueueSize());
		}
	}

	private void setCallAndConfState(BaseEvent event) {
		String eventType = event.getType();
		Call call = event.getCall();
		//set conf state
		if (eventType.equals(EventType.JOINCONF)) {
			ConfEvent ev = (ConfEvent) event;
			CONF_MODE mode = CONF_MODE.valueOf(ev.getMode());
			manager.addMember(ev.getConfID(), call, mode);
		} else if (eventType.equals(EventType.LEAVECONF)) {
			//如果是离开会议，移除会议成员
			ConfEvent ev = (ConfEvent) event;
			manager.findConfByConfID(ev.getConfID()).removeMember(call);
		} else if (eventType.equals(EventType.CLEARCONF)) {
			//如果是清除会议，移除整个会议
			ConfEvent ev = (ConfEvent) event;
			manager.removeConf(ev.getConfID());
		}
		//set call state
		if (call != null) {
			if (eventType.equals(EventType.ALERT)) {
				//ISDN不会触发此事件
				if (event.getEventState() == Constants.RET_SUCCESS) {
					call.setState(Constants.CALL_ALERT);
				}
			} else if (eventType.equals(EventType.OFFHOOK)) {
				if (event.getEventState() == Constants.RET_SUCCESS) {
					call.setState(Constants.CALL_CONTENT);
				}
			} else if (eventType.equals(EventType.CALLOUT)) {
				call.setState(event.getEventState() == Constants.RET_SUCCESS ? Constants.CALL_CONTENT : Constants.CALL_IDLE);
			} else if (eventType.equals(EventType.PLAYEND)) {
				call.setIsPlaying(false);
			} 
		}
	}
}
