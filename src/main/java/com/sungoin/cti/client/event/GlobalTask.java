/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import org.slf4j.MDC;

/**
 *
 * <AUTHOR> 2015-6-9
 */
public class GlobalTask implements Runnable {

	public static final int CALL_INCOME = 1;
	public static final int CALL_END = 2;
	private final Call call;
	private final int type;

	public GlobalTask(Call call, int type) {
		this.call = call;
		this.type = type;
	}

	@Override
	public void run() {
        MDC.put(Constants.TRACE_ID, call.getTraceId());
		CallManager manager = CallManager.getInstance();
		if (type == CALL_INCOME) {
			call.setState(Constants.CALL_ING);
			manager.putCall(call);
			manager.getListener().callIncome(call);
		} else {
			//挂机
			int state = call.getState();
			if (state == Constants.CALL_CONTENT) {
				call.stopReceiveDTMF();
			}
			call.setState(Constants.CALL_IDLE);
			manager.getListener().callEnd(call);
		}
        MDC.remove(Constants.TRACE_ID);
	}

}
