/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

/**
 *
 * <AUTHOR> 2015-5-14
 */
public class CallIncomeEvent extends BaseEvent {

	private String caller;
	private String callee;

	public CallIncomeEvent(String caller, String callee, String eventType, int lsh, int eventState, String threadName, String deviceID, int errorCode) {
		super(eventType, lsh, eventState, threadName, deviceID, errorCode);
		this.caller = caller;
		this.callee = callee;
	}

	public String getCaller() {
		return caller;
	}

	public void setCaller(String caller) {
		this.caller = caller;
	}

	public String getCallee() {
		return callee;
	}

	public void setCallee(String callee) {
		this.callee = callee;
	}

}
