/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

import com.sungoin.cti.client.api.CallManager;
import com.sungoin.cti.client.api.TeleThread;

/**
 *
 * <AUTHOR> 2015-6-3
 */
public class EventExecuteTask implements Runnable {

	private final BaseEvent event;

	public EventExecuteTask(BaseEvent event) {
		this.event = event;
	}

	@Override
	public void run() {
		if (event.getThreadName() == null) {
			//触发全局的事件监听
			CallManager.getInstance().getListener().asyncFinished(event);
		} else {
			TeleThread thread = CallManager.getInstance().getTeleThread(event.getThreadName());
			if (thread != null) {
				//触发线程的事件监听
				thread.getListener().asyncFinished(event);
			} else {
				//触发全局的事件监听
				CallManager.getInstance().getListener().asyncFinished(event);
			}
		}
	}

}
