/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

import java.util.Map;

/**
 *
 * <AUTHOR> 2015-5-14
 */
public class EventUtil {

	public static final BaseEvent parseEvent(Map dataMap) {
		String eventType = (String) dataMap.get("eventType");
		BaseEvent event = null;
		int lsh = Integer.parseInt(dataMap.get("lsh").toString());
		int eventState = Integer.parseInt(dataMap.get("eventState").toString());
		String threadName = (String) dataMap.get("threadName");
		String deviceID = (String) dataMap.get("deviceID");
        int errorCode = 0;
        if(dataMap.containsKey("errorCode")) {
            errorCode = Integer.parseInt(dataMap.get("errorCode").toString());
        }
		if (eventType.equals(EventType.CALL_INCOME)) {
			String caller = (String) dataMap.get("caller");
			String callee = (String) dataMap.get("callee");
			event = new CallIncomeEvent(caller, callee,eventType, lsh, eventState, threadName, deviceID, errorCode);
		} else if (eventType.equals(EventType.RECEIVEDTMF)) {
			String dtmf = (String) dataMap.get("dtmf");
			event = new ReceiveDTMFEvent(dtmf,  eventType, lsh, eventState, threadName, deviceID, errorCode);
		} else if (eventType.equals(EventType.JOINCONF) || eventType.equals(EventType.LEAVECONF)|| eventType.equals(EventType.CLEARCONF)){
            String mode =(String) dataMap.get("mode");
            String confID =(String) dataMap.get("confID");
            event =new ConfEvent(mode, confID, eventType, lsh, eventState, threadName, deviceID, errorCode);
        } else if (eventType.equals(EventType.CDR)){
            String record =(String) dataMap.get("record");
            event =new CdrEvent(record, eventType, lsh, eventState, threadName, deviceID, errorCode);
        } else if (eventType.equals(EventType.DETECTSPEECH)){
            String text =(String) dataMap.get("text");
            event =new DetectSpeechEvent(text, eventType, lsh, eventState, threadName, deviceID, errorCode);
        } else {
			//通用event
			event = new BaseEvent(eventType, lsh, eventState, threadName, deviceID, errorCode);
		}
		return event;
	}
}
