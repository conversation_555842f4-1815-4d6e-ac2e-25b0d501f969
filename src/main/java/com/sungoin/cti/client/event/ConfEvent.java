/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

/**
 *
 * <AUTHOR>
 */
public class ConfEvent extends  BaseEvent{
    
    private String mode;
    private String confID;

    public ConfEvent(String mode, String confID, String eventType, int lsh, int eventState, String threadName, String deviceID, int errorCode) {
        super(eventType, lsh, eventState, threadName, deviceID, errorCode);
        this.mode = mode;
        this.confID = confID;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getConfID() {
        return confID;
    }

    public void setConfID(String confID) {
        this.confID = confID;
    }
}
