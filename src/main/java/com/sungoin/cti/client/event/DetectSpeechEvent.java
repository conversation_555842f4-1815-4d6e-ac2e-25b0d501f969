/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.client.event;

/**
 *
 * <AUTHOR>
 */
public class DetectSpeechEvent extends BaseEvent {
    private final String text;

    public DetectSpeechEvent(String text, String eventType, int lsh, int eventState, String threadName, String deviceID, int errorCode) {
        super(eventType, lsh, eventState, threadName, deviceID, errorCode);
		this.text = text;
    }

    public String getText() {
        return text;
    }
    
    
}
