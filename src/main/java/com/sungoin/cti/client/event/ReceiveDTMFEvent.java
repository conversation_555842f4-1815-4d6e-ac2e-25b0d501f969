/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.event;

/**
 *
 * <AUTHOR> 2015-5-14
 */
public class ReceiveDTMFEvent extends BaseEvent {

	private String dtmf;

	public ReceiveDTMFEvent(String dtmf, String eventType, int lsh, int eventState, String threadName, String deviceID, int errorCode) {
		super(eventType, lsh, eventState, threadName, deviceID, errorCode);
		this.dtmf = dtmf;
	}
	
	public String getDtmf() {
		return dtmf;
	}

	public void setDtmf(String dtmf) {
		this.dtmf = dtmf;
	}

    @Override
    public String toString() {
        return "ReceiveDTMFEvent{" + "dtmf=" + dtmf + ",lsh=" + lsh+ '}';
    }

	
}
