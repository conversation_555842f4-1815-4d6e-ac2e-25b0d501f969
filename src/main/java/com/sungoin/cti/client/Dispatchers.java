/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client;

import com.sungoin.cti.client.event.EventDispatcher;
import com.sungoin.cti.client.message.MessageDispatcher;

/**
 *
 * <AUTHOR>
 */
public class Dispatchers {
	
	public static Dispatcher getEventDispatcher() {
		return EventDispatcher.getInstance();
	}
	
	public static Dispatcher getMessageDispatcher() {
		return MessageDispatcher.getInstance();
	}
	
}
