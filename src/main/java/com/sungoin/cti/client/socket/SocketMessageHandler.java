/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.client.socket;

import com.sungoin.cti.client.Dispatchers;
import com.sungoin.cti.client.event.EventUtil;
import com.sungoin.cti.client.message.MessageUtil;
import com.sungoin.cti.client.util.JsonHelper;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 
 * <AUTHOR>
 * 2015-5-13
 */
public class SocketMessageHandler {
	private static final SocketMessageHandler instance = new SocketMessageHandler();
	
	private static final Logger log = LoggerFactory.getLogger(SocketMessageHandler.class);

	private SocketMessageHandler() {
	}
	
	public static SocketMessageHandler getInstance() {
		return instance;
	}
	
	public void process(String message) {
		Map map = JsonHelper.json2Object(message, Map.class);
		if(map.containsKey("errorMsg")) {
			log.error("中间件server返回错误信息：{}",map.get("errorMsg"));
		} else if(map.containsKey("messageType")) {
			//消息
			Dispatchers.getMessageDispatcher().dispatch(MessageUtil.parseResponse(map));
		} else if(map.containsKey("eventType")) {
			//事件
			Dispatchers.getEventDispatcher().dispatch(EventUtil.parseEvent(map));
		} else {
			log.error("unknow message handler!msg={}",message);
		}
	}
}
