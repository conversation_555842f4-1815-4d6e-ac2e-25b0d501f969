/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.client.socket;

import com.sungoin.cti.client.Configuration;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.filter.logging.LoggingFilter;
import org.apache.mina.transport.socket.nio.NioSocketConnector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 
 * <AUTHOR>
 * 2015-5-13
 */
public class MinaClient {
// 记录日志
    /** The Constant log. */
    private static final Logger log = LoggerFactory.getLogger(MinaClient.class);

    /** The cf. */
    private ConnectFuture cf = null;

    /** The connector. */
    private NioSocketConnector connector = null;

    /** The is connected. */
    public volatile boolean isConnected = false;

    /** The terminated. */
    public volatile boolean terminated = false;
	
	private final String serverip = Configuration.getServerIp();
	
	private final int port = Configuration.getServerPort();
	
	private final int reconnectTimeSecond = Configuration.getReconnectTime();

    /** The Constant connectorMgr. */
    private static final MinaClient instance = new MinaClient();
	
	private final ExecutorService es = Executors.newCachedThreadPool();

    /**
     * Instantiates a new platform connector mgr.
     */
    private MinaClient() {
    }

    /**
     * Gets the single instance of PlatformConnectorMgr.
     * 
     * @return single instance of PlatformConnectorMgr
     */
    public static MinaClient getInstance() {
        return instance;
    }

    /**
     * Gets the cf.
     * 
     * @return the cf
     */
    public ConnectFuture getCf() {
        return cf;
    }

	public String getServerip() {
		return serverip;
	}

	public int getPort() {
		return port;
	}

	public int getReconnectTimeSecond() {
		return reconnectTimeSecond;
	}
	
    /**
     * Inits the connector.
     * 
     * @return true, if successful
     */
    public synchronized boolean initConnector() {
        try {
            log.info("init platform socket Connector,serverip:" + serverip + " port:" + port);
			if(serverip == null || port ==0) {
				throw new IllegalStateException("serverIP or serverPort not setted...");
			}
            connector = new NioSocketConnector();
            InetAddress address = InetAddress.getByName(serverip);
            connector.getFilterChain().addLast("logger", new LoggingFilter());
			connector.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
            connector.setConnectTimeoutMillis(10000);
            connector.setHandler(new ClientHandler());// 设置事件处理器
            cf = connector.connect(new InetSocketAddress(address, port));// 建立连接
            //等待连接创建完成,
            //如果不加这句则在连接异常时getSession()并不会抛异常,获得的SESSION为null
            cf.awaitUninterruptibly(1000);
            if (cf.isConnected()) {
                isConnected = true;
                log.info("CTI SERVER 连接成功");
            } else {
                isConnected = false;
                connector.dispose();
                log.warn("CTI SERVER 连接失败");
            }
        } catch (Exception e) {
            isConnected = false;
            connector.dispose();
            log.error("在MinaClient中的connect方法中出错原因是:", e);
        }
        return isConnected;
    }

    /**
     * Interrupt.
     */
    public synchronized void interrupt() {
        isConnected = false;
        if (connector != null && !connector.isDisposed()) {
            connector.dispose();
        }
    }

    /**
     * Close.
     */
    public synchronized void close() {
        isConnected = false;
        terminated = true;
        connector.dispose();
		es.shutdownNow();
    }

	public void sendMessage(String message) {
		String msg = message + "\r";
        try {
            cf.getSession().write(msg);// 发送消息
        } catch (Exception e) {
            log.error("在MinaClient中的sendMessage方法中出错原因是:" + e.getMessage());
        }
    }
	
	public void sendMessageAsync(final String message) {
		es.submit(new Runnable() {
			@Override
			public void run() {
				try {
					Thread.sleep(200);
				} catch (Exception ex) {
					log.error(ex.getMessage(),ex);
				}
				MinaClient.this.sendMessage(message);
			}
		});
	}
}
