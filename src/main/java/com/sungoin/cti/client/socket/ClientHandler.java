/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.socket;

import java.util.concurrent.TimeUnit;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-13
 */
public class ClientHandler extends IoHandlerAdapter {

	private static final Logger log = LoggerFactory.getLogger(ClientHandler.class);

	@Override
	public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
		log.error("exceptionCaught method was called..." + cause.getMessage(), cause);
//		MinaClient.getInstance().interrupt();
        this.sessionClosed(session);
	}

	@Override
	public void sessionCreated(IoSession session) throws Exception {
        log.info("session created id : {}", session.getId());
		session.getConfig().setIdleTime(IdleStatus.BOTH_IDLE, 20);
	}

	@Override
	public void messageReceived(IoSession session, Object message) throws Exception {
		String msg = message.toString();
		log.debug("session id : {}, message received : {}", session.getId(),msg);
		SocketMessageHandler.getInstance().process(msg);
	}

	@Override
	public void sessionIdle(IoSession session, IdleStatus status) throws Exception {
		if (status == IdleStatus.BOTH_IDLE) {
			log.debug("session id : {}, sessionIdle method was called ! heartbeat...", session.getId());
			session.write("heartBeat\r");
		}
	}

	@Override
	public void sessionClosed(IoSession session) throws Exception {
		log.warn("session id : {}, sessionClosed method was called!", session.getId());
        MinaClient mc = MinaClient.getInstance();
        mc.interrupt();
        while (!mc.isConnected) {
            if (mc.terminated) {
                break;
            }
            try {
                TimeUnit.SECONDS.sleep(mc.getReconnectTimeSecond());
                log.info("开始重新连接CTI SERVER服务器：");
                mc.initConnector();
            } catch (Exception ex) {
                log.error("重新连接CTI SERVER服务器失败！" + ex.getMessage());
            }
        }
	}

}
