/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client.exception;


/**
 *
 * <AUTHOR> 2015-5-5
 */
public class AlreadyWaitingException extends CTICheckedException {

	private static final long serialVersionUID = 8791563196688180997L;

	public AlreadyWaitingException() {
		super();
	}

	public AlreadyWaitingException(String msg) {
		super(msg);
	}

	public AlreadyWaitingException(Throwable root) {
		super(root);
	}

	public AlreadyWaitingException(String msg, Throwable cause) {
		super(msg, cause);
	}
}
