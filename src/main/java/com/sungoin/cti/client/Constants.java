/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client;

/**
 *
 * <AUTHOR> 2015-5-14
 */
public class Constants {

	public static final int RET_SUCCESS = 1;
	public static final int RET_FAIL = 0;
	public static final int RET_CALL_STATE_FAIL = -1;
	public static final int RET_TIMEOUT = -2;

	public static final String MESSAGE_QUEUE_SIZE = "message.queue.size";
	public static final String EVENT_QUEUE_SIZE = "event.queue.size";

	public static final String SOCKET_TIMEOUT_TIMEMILLIS = "socket.timeout.timeMillis";
	public static final String CALLEND_DESTROY_DELAY_TIMEMILLIS = "callend.destroy.delay.timeMillis";

	public static final String CUSTOM_PROP_NAME = "cti.properties";
	public static final String DEFAULT_PROP_NAME = "cti_default.properties";

	public static final String SERVER_IP = "cti.server.ip";
	public static final String SERVER_PORT = "cti.server.port";
	public static final String RECONNECT_TIME = "cti.reconnect.time";

	public static final int CALL_IDLE = 0; //空闲
	public static final int CALL_ING = 1;  //呼叫中
	public static final int CALL_ALERT = 2;  //振铃
	public static final int CALL_CONTENT = 3;  //通话中

}
