/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.client;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class KeyUp {

	private static final Logger log = LoggerFactory.getLogger(KeyUp.class);

	private static final Map<String, KeyUp> dtfmMap = new ConcurrentHashMap<String, KeyUp>();

	private final String endKey;
	private final int maxdigits;
	private final StringBuilder dfmf = new StringBuilder();

	public KeyUp(String endKey, int maxdigits) {
		this.endKey = endKey;
		this.maxdigits = maxdigits;
	}
	
	private String append(String key) {
		String code = null;
          //一、判斷是否追加
		// 1. endKey 為空 按鍵長度等於收碼長度返回
		// 2. endKey 不為空 如果按鍵長度小於收碼長度返回按鍵   如果按鍵長度大於收碼長度返回收碼長度
		// 二、判斷是否返回

		//如果沒有輸入結束字符串
		if (endKey == null || endKey.length() == 0) {
			if (dfmf.length() < maxdigits) {
				dfmf.append(key);
			}
			if (dfmf.length() >= maxdigits) {
				code = dfmf.toString();
				dfmf.delete(0,dfmf.length());
			}
		} else {
			// 2位 1# =1 123# 12
			if (!key.equals(endKey) && dfmf.length() < maxdigits) {
				dfmf.append(key);
				return null;
			}
			if (key.equals(endKey)) {
				code = dfmf.toString();
				dfmf.delete(0,dfmf.length());
			}
		}

		return code;
	}

	public static void initDtfm(String deviceID, String endKey, int maxdigits) {
		KeyUp KeyUp = new KeyUp(endKey, maxdigits);
		dtfmMap.put(deviceID, KeyUp);

	}

	public static String appendDTMF(String deviceID, String key) {
		if (dtfmMap.get(deviceID) == null) {
			return null;
		}
		String code = dtfmMap.get(deviceID).append(key);
		return code;
	}

	public static void cleanDtmf(String deviceID) {
		dtfmMap.remove(deviceID);
        log.debug(" dtfmMap size is {}",dtfmMap.size());
	}
	
}
