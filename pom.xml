<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.sungoin.cti</groupId>
	<artifactId>cti-client</artifactId>
	<version>1.3.17</version>
	<packaging>jar</packaging>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
	</properties>
	<dependencies>
		<!--log4j  -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>2.3</version>
		</dependency>
		<!--mina -->
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-core</artifactId>
			<version>2.0.9</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.1</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.5.4</version>
		</dependency>
        </dependencies>
	     <repositories>
			<repository>
				<id>sungoin-releases </id>
				<name>inner nexus server </name>
				<url>http://nexus.sungoin.com/repository/maven-public</url>
			</repository>
		</repositories>
		<pluginRepositories>
			<pluginRepository>
				<id>sungoin-releases</id>
				<name>inner nexus plugin server</name>
				<url>http://nexus.sungoin.com/repository/maven-public</url>
			</pluginRepository>
		</pluginRepositories>
	
	
		<distributionManagement>
	        <repository>
	            <!--这里的id需要和settings.xml中的server的id一致-->
	            <id>sungoin-release</id>
	            <name>Nexus release Repository</name>
	            <url>http://nexus.sungoin.com/repository/sungoin-release/</url>
	        </repository>
	    </distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>appassembler-maven-plugin</artifactId>
				<version>1.10</version>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>assemble</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<configurationDirectory>conf</configurationDirectory>
					<configurationSourceDirectory>src/main/resources</configurationSourceDirectory>
					<copyConfigurationDirectory>true</copyConfigurationDirectory>
					<includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>
					<assembleDirectory>${project.build.directory}/CTI-CLIENT-RELEASE</assembleDirectory>
					<binFileExtensions>
						<unix>.sh</unix>
					</binFileExtensions>
					<platforms>
						<platform>windows</platform>
						<!--<platform>unix</platform>-->
					</platforms>
					<repositoryName>lib</repositoryName>
					<programs>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase1</mainClass>
							<id>startup_testCase1</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase2</mainClass>
							<id>startup_testCase2</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase3</mainClass>
							<id>startup_testCase3</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase4</mainClass>
							<id>startup_testCase4</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase5</mainClass>
							<id>startup_testCase5</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase6</mainClass>
							<id>startup_testCase6</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase7</mainClass>
							<id>startup_testCase7</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase8</mainClass>
							<id>startup_testCase8</id>
						</program>
						
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase9</mainClass>
							<id>startup_testCase9</id>
						</program>
						 
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase10</mainClass>
							<id>startup_testCase10</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase11</mainClass>
							<id>startup_testCase11</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase12</mainClass>
							<id>startup_testCase12</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase13</mainClass>
							<id>startup_testCase13</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase14</mainClass>
							<id>startup_testCase14</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase15</mainClass>
							<id>startup_testCase15</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase16</mainClass>
							<id>startup_testCase16</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase17</mainClass>
							<id>startup_testCase17</id>
						</program>
						<program>
							<mainClass>com.sungoin.cti.client.demo.TestCase18</mainClass>
							<id>startup_testCase18</id>
						</program>
					</programs>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>